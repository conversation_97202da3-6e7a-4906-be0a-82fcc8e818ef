# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.0"
    "@jridgewell/trace-mapping": "npm:^0.3.9"
  checksum: 10c0/92ce5915f8901d8c7cd4f4e6e2fe7b9fd335a29955b400caa52e0e5b12ca3796ada7c2f10e78c9c5b0f9c2539dff0ffea7b19850a56e1487aa083531e1e46d43
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.10.4, @babel/code-frame@npm:~7.10.4":
  version: 7.10.4
  resolution: "@babel/code-frame@npm:7.10.4"
  dependencies:
    "@babel/highlight": "npm:^7.10.4"
  checksum: 10c0/69e0f52986a1f40231d891224f420436629b6678711b68c088e97b7bdba1607aeb5eb9cfb070275c433f0bf43c37c134845db80d1cdbf5ac88a69b0bdcce9402
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.20.5, @babel/compat-data@npm:^7.27.2, @babel/compat-data@npm:^7.27.7":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 10c0/c4e527302bcd61052423f757355a71c3bc62362bac13f7f130de16e439716f66091ff5bdecda418e8fa0271d4c725f860f0ee23ab7bf6e769f7a8bb16dfcb531
  languageName: node
  linkType: hard

"@babel/core@npm:^7.13.16, @babel/core@npm:^7.20.0, @babel/core@npm:^7.24.0":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:7.2.0":
  version: 7.2.0
  resolution: "@babel/generator@npm:7.2.0"
  dependencies:
    "@babel/types": "npm:^7.2.0"
    jsesc: "npm:^2.5.1"
    lodash: "npm:^4.17.10"
    source-map: "npm:^0.5.0"
    trim-right: "npm:^1.0.1"
  checksum: 10c0/cbcc4a5380976c68b1725f8e1566f0f0706464628d42931f836e1034a06e3dfffac17283ebb37cc0e5dc38db39af0aa1ed29c9c3686ea028b8e105e23cc14436
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.0, @babel/generator@npm:^7.20.5, @babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": "npm:^7.28.0"
    "@babel/types": "npm:^7.28.0"
    "@jridgewell/gen-mapping": "npm:^0.3.12"
    "@jridgewell/trace-mapping": "npm:^0.3.28"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/1b3d122268ea3df50fde707ad864d9a55c72621357d5cebb972db3dd76859c45810c56e16ad23123f18f80cc2692f5a015d2858361300f0f224a05dc43d36a92
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1, @babel/helper-annotate-as-pure@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/94996ce0a05b7229f956033e6dcd69393db2b0886d0db6aff41e704390402b8cdcca11f61449cb4f86cfd9e61b5ad3a73e4fa661eeed7846b125bd1c33dbc633
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.20.7, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.5":
  version: 0.6.5
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.5"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    debug: "npm:^4.4.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.22.10"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/4886a068d9ca1e70af395340656a9dda33c50502c67eed39ff6451785f370bdfc6e57095b90cb92678adcd4a111ca60909af53d3a741120719c5604346ae409e
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.18.9":
  version: 7.24.7
  resolution: "@babel/helper-environment-visitor@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 10c0/36ece78882b5960e2d26abf13cf15ff5689bf7c325b10a2895a74a499e712de0d305f8d78bb382dd3c05cfba7e47ec98fe28aab5674243e0625cd38438dd0b2d
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: 10c0/5a0cd0c0e8c764b5f27f2095e4243e8af6fa145daea2b41b53c0c1414fe6ff139e3640f4e2207ae2b3d2153a1abd346f901c26c290ee7cb3881dd922d4ee9232
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.18.9, @babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.20.0, @babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.24.7, @babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4":
  version: 7.24.7
  resolution: "@babel/highlight@npm:7.24.7"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.24.7"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/674334c571d2bb9d1c89bdd87566383f59231e16bcdcf5bb7835babdf03c9ae585ca0887a7b25bdf78f303984af028df52831c7989fecebb5101cc132da9393a
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.13.16, @babel/parser@npm:^7.20.0, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": "npm:^7.28.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/c2ef81d598990fa949d1d388429df327420357cb5200271d0d0a2784f1e6d54afc8301eb8bdf96d8f6c77781e402da93c7dc07980fcc136ac5b9d5f1fce701b5
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.0.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.20.7"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.18.9"
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/helper-remap-async-to-generator": "npm:^7.18.9"
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0f4bc01805704ae4840536acc9888c50a32250e9188d025063bd17fe77ed171a12361c3dc83ce99664dcd73aec612accb8da95b0d8b825c854931b2860c0bfb5
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.13.0, @babel/plugin-proposal-class-properties@npm:^7.18.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d5172ac6c9948cdfc387e94f3493ad86cb04035cf7433f86b5d358270b1b9752dc25e176db0c5d65892a246aca7bdb4636672e15626d7a7de4bc0bd0040168d9
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.12.9":
  version: 7.28.0
  resolution: "@babel/plugin-proposal-decorators@npm:7.28.0"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-decorators": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e399f3adc4278560d15fd80691c7a9b644f46e50fa90746f9f3b9ac02cf955ef2b6677277d97c97a4bd6d6a777821fdedf1318923632a439cba1c05e8e59246c
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-default-from@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6e0756e0692245854028caea113dad2dc11fcdd479891a59d9a614a099e7e321f2bd25a1e3dd6f3b36ba9506a76f072f63adbf676e5ed51e7eeac277612e3db2
  languageName: node
  linkType: hard

"@babel/plugin-proposal-logical-assignment-operators@npm:^7.18.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-logical-assignment-operators@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/436c1ee9f983813fc52788980a7231414351bd34d80b16b83bddb09115386292fe4912cc6d172304eabbaf0c4813625331b9b5bc798acb0e8925cf0d2b394d4d
  languageName: node
  linkType: hard

"@babel/plugin-proposal-nullish-coalescing-operator@npm:^7.13.8, @babel/plugin-proposal-nullish-coalescing-operator@npm:^7.18.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-nullish-coalescing-operator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f6629158196ee9f16295d16db75825092ef543f8b98f4dfdd516e642a0430c7b1d69319ee676d35485d9b86a53ade6de0b883490d44de6d4336d38cdeccbe0bf
  languageName: node
  linkType: hard

"@babel/plugin-proposal-numeric-separator@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-numeric-separator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.18.6"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a83a65c6ec0d2293d830e9db61406d246f22d8ea03583d68460cb1b6330c6699320acce1b45f66ba3c357830720e49267e3d99f95088be457c66e6450fbfe3fa
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.20.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": "npm:^7.20.5"
    "@babel/helper-compilation-targets": "npm:^7.20.7"
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-transform-parameters": "npm:^7.20.7"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b9818749bb49d8095df64c45db682448d04743d96722984cbfd375733b2585c26d807f84b4fdb28474f2d614be6a6ffe3d96ffb121840e9e5345b2ccc0438bd8
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ab20153d9e95e0b73004fdf86b6a2d219be2a0ace9ca76cd9eccddb680c913fec173bca54d761b1bc6044edde0a53811f3e515908c3b16d2d81cfec1e2e17391
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-chaining@npm:^7.13.12, @babel/plugin-proposal-optional-chaining@npm:^7.20.0":
  version: 7.21.0
  resolution: "@babel/plugin-proposal-optional-chaining@npm:7.21.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.20.0"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b524a61b1de3f3ad287cd1e98c2a7f662178d21cd02205b0d615512e475f0159fa1b569fa7e34c8ed67baef689c0136fa20ba7d1bf058d186d30736a581a723f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46ef933bae10b02a8f8603b2f424ecbe23e134a133205bee7c0902dae3021c183a683964cab41ea5433820aa05be0f6f36243551f68a1d94e02ac082cec87aa1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.0":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9c50927bf71adf63f60c75370e2335879402648f468d0172bc912e303c6a3876927d8eb35807331b57f415392732ed05ab9b42c68ac30a936813ab549e0246c5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-default-from@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9aa62f5916950f3e5f91657895f4635b1c77e108e453ef12c30dc7670c3441bdd65cd28be20d6ddc9003ed471cc98465785a14cd76c61f077c1c84264f1f28ca
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.12.1, @babel/plugin-syntax-flow@npm:^7.18.0, @babel/plugin-syntax-flow@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4d34ca47044398665cbe0293baea7be230ca4090bc7981ffba5273402a215c95976c6f811c7b32f10b326cc6aab6886f26c29630c429aa45c3f350c5ccdfdbbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.0.0, @babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.0.0, @babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.20.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.0.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/787d85e72a92917e735aa54e23062fa777031f8a07046e67f5026eff3d91e64eb535575dd1df917b0011bee014ae51287478af14c1d4ba60bc81e326bc044cfc
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.0.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-classes@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/3b213b43104fe99dd7e79401a86d09e545836e057a70ffe77e8196a87bf67ae167e502ae90afdf0d1a2be683be5652514aaeda743bd984e583523dd8ecfef887
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.20.0, @babel/plugin-transform-destructuring@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-destructuring@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cc7ccafa952b3ff7888544d5688cfafaba78c69ce1e2f04f3233f4f78c9de5e46e9695f5ea42c085b0c0cfa39b10f366d362a2be245b6d35b66d3eb1d427ccb2
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.22.11":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d7165cad11f571a54c8d9263d6c6bf2b817aff4874f747cb51e6e49efb32f2c9b37a6850cdb5e3b81e0b638141bb77dc782a6ec1a94128859fbdf7767581e07c
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.20.0, @babel/plugin-transform-flow-strip-types@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-flow": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c61c43244aacdcd479ad9ba618e1c095a5db7e4eadc3d19249602febc4e97153230273c014933f5fe4e92062fa56dab9bed4bc430197d5b2ffeb2158a4bf6786
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.0.0, @babel/plugin-transform-modules-commonjs@npm:^7.13.8, @babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.12.13":
  version: 7.28.0
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.28.0"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.28.0"
    "@babel/plugin-transform-parameters": "npm:^7.27.7"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/360dc6fd5285ee5e1d3be8a1fb0decd120b2a1726800317b4ab48b7c91616247030239b7fa06ceaa1a8a586fde1e143c24d45f8d41956876099d97d664f8ef1e
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.0.0, @babel/plugin-transform-parameters@npm:^7.20.7, @babel/plugin-transform-parameters@npm:^7.22.15, @babel/plugin-transform-parameters@npm:^7.27.7":
  version: 7.27.7
  resolution: "@babel/plugin-transform-parameters@npm:7.27.7"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f2da3804e047d9f1cfb27be6c014e2c7f6cf5e1e38290d1cb3cb2607859e3d6facb4ee8c8c1e336e9fbb440091a174ce95ce156582d7e8bf9c0e735d11681f0f
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/232bedfe9d28df215fb03cc7623bdde468b1246bdd6dc24465ff4bf9cc5f5a256ae33daea1fafa6cc59705e4d29da9024bb79baccaa5cd92811ac5db9b9244f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.22.11":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a8c4536273ca716dcc98e74ea25ca76431528554922f184392be3ddaf1761d4aa0e06f1311577755bd1613f7054fb51d29de2ada1130f743d329170a1aa1fe56
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.0.0, @babel/plugin-transform-react-display-name@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-react-display-name@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f5f86d2ad92be3e962158f344c2e385e23e2dfae7c8c7dc32138fb2cc46f63f5e50386c9f6c6fc16dbf1792c7bb650ad92c18203d0c2c0bd875bc28b0b80ef30
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/eb8c4b6a79dc5c49b41e928e2037e1ee0bbfa722e4fd74c0b7c0d11103c82c2c25c434000e1b051d534c7261ab5c92b6d1e85313bf1b26e37db3f051ae217b58
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.0.0, @babel/plugin-transform-react-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1a08637c39fc78c9760dd4a3ed363fdbc762994bf83ed7872ad5bda0232fcd0fc557332f2ce36b522c0226dfd9cc8faac6b88eddda535f24825198a689e571af
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/34bc090f4a7e460d82a851971b4d0f32e4bb519bafb927154f4174506283fe02b0f471fc20655c6050a8bf7b748bfa31c7e8f7d688849476d8266623554fbb28
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.0.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-runtime@npm:7.28.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    babel-plugin-polyfill-corejs2: "npm:^0.4.14"
    babel-plugin-polyfill-corejs3: "npm:^0.13.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.5"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/71eba1e6aaafacb2ec0fd468394c7aeaff32265b21424bd9b2d963368a4a5260547e06976bb34e2553a7179463c3a3a4c2a4552256b5112c8b7dcadb7bd5bb07
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.27.1, @babel/plugin-transform-typescript@npm:^7.5.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-typescript@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/049c2bd3407bbf5041d8c95805a4fadee6d176e034f6b94ce7967b92a846f1e00f323cf7dfbb2d06c93485f241fb8cf4c10520e30096a6059d251b94e80386e9
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/preset-flow@npm:^7.13.13":
  version: 7.27.1
  resolution: "@babel/preset-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/252216c91ba3cc126f10c81c1df495ef2c622687d17373bc619354a7fb7280ea83f434ed1e7149dbddd712790d16ab60f5b864d007edd153931d780f834e52c1
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.22.15":
  version: 7.27.1
  resolution: "@babel/preset-react@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-transform-react-display-name": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx-development": "npm:^7.27.1"
    "@babel/plugin-transform-react-pure-annotations": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a80b02ef08b026cb9830d6512d08c7cd378eef4c0631dacba4aa1106240d9bb76af6373463f0255f4bbdbfcce40375a61e92735375906ba5871629b0c314bc45
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.13.0, @babel/preset-typescript@npm:^7.23.0":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cba6ca793d915f8aff9fe2f13b0dfbf5fd3f2e9a17f17478ec9878e9af0d206dcfe93154b9fd353727f16c1dca7c7a3ceb4943f8d28b216235f106bc0fbbcaa3
  languageName: node
  linkType: hard

"@babel/register@npm:^7.13.16":
  version: 7.27.1
  resolution: "@babel/register@npm:7.27.1"
  dependencies:
    clone-deep: "npm:^4.0.1"
    find-cache-dir: "npm:^2.0.0"
    make-dir: "npm:^2.1.0"
    pirates: "npm:^4.0.6"
    source-map-support: "npm:^0.5.16"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9584f6c5d980aa7eb6f56f56dfc12fa01a47ab11d542908192cb455a5249d489ab24efcd5de7c1b8be0fb47cd5594e4ee5652c58ba9b857fb81e783541c6a0ff
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.25.0, @babel/runtime@npm:^7.6.2, @babel/runtime@npm:^7.8.7":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.0.0, @babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.20.0, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4, @babel/traverse@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.0"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/parser": "npm:^7.28.0"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.0"
    debug: "npm:^4.3.1"
  checksum: 10c0/32794402457827ac558173bcebdcc0e3a18fa339b7c41ca35621f9f645f044534d91bb923ff385f5f960f2e495f56ce18d6c7b0d064d2f0ccb55b285fa6bc7b9
  languageName: node
  linkType: hard

"@babel/types@npm:^7.19.0, @babel/types@npm:^7.2.0, @babel/types@npm:^7.20.0, @babel/types@npm:^7.24.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.28.0":
  version: 7.28.1
  resolution: "@babel/types@npm:7.28.1"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/5e99b346c11ee42ffb0cadc28159fe0b184d865a2cc1593df79b199772a534f6453969b4942aa5e4a55a3081863096e1cc3fc1c724d826926dc787cf229b845d
  languageName: node
  linkType: hard

"@egjs/hammerjs@npm:^2.0.17":
  version: 2.0.17
  resolution: "@egjs/hammerjs@npm:2.0.17"
  dependencies:
    "@types/hammerjs": "npm:^2.0.36"
  checksum: 10c0/dbedc15a0e633f887c08394bd636faf6a3abd05726dc0909a0e01209d5860a752d9eca5e512da623aecfabe665f49f1d035de3103eb2f9022c5cea692f9cc9be
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^1.2.1":
  version: 1.2.1
  resolution: "@emotion/is-prop-valid@npm:1.2.1"
  dependencies:
    "@emotion/memoize": "npm:^0.8.1"
  checksum: 10c0/7c2aabdf0ca9986ca25abc9dae711348308cf18d418d64ffa4c8ffd2114806c47f2e06ba8ee769f38ec67d65bd59ec73f34d94023e81baa1c43510ac86ccd5e6
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: 10c0/dffed372fc3b9fa2ba411e76af22b6bb686fb0cb07694fdfaa6dd2baeb0d5e4968c1a7caa472bfcf06a5997d5e7c7d16b90e993f9a6ffae79a2c3dbdc76dfe78
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.8.0":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 10c0/a1ed508628288f40bfe6dd17d431ed899c067a899fa293a13afe3aed1d70fac0412b8a215fafab0b42829360db687fecd763e5f01a64ddc4a4b58ec3112ff548
  languageName: node
  linkType: hard

"@expo/bunyan@npm:^4.0.0":
  version: 4.0.1
  resolution: "@expo/bunyan@npm:4.0.1"
  dependencies:
    uuid: "npm:^8.0.0"
  checksum: 10c0/ebbec51c7b19dcfcbd981da9c1c6262c0dc03ea118356fefca3b427f445308845fc33d97da92350d68fda174f9f1d5ee95ed3ac978f1f6cc88de73d785b909cc
  languageName: node
  linkType: hard

"@expo/cli@npm:0.18.31":
  version: 0.18.31
  resolution: "@expo/cli@npm:0.18.31"
  dependencies:
    "@babel/runtime": "npm:^7.20.0"
    "@expo/code-signing-certificates": "npm:0.0.5"
    "@expo/config": "npm:~9.0.0-beta.0"
    "@expo/config-plugins": "npm:~8.0.8"
    "@expo/devcert": "npm:^1.0.0"
    "@expo/env": "npm:~0.3.0"
    "@expo/image-utils": "npm:^0.5.0"
    "@expo/json-file": "npm:^8.3.0"
    "@expo/metro-config": "npm:0.18.11"
    "@expo/osascript": "npm:^2.0.31"
    "@expo/package-manager": "npm:^1.5.0"
    "@expo/plist": "npm:^0.1.0"
    "@expo/prebuild-config": "npm:7.0.9"
    "@expo/rudder-sdk-node": "npm:1.1.1"
    "@expo/spawn-async": "npm:^1.7.2"
    "@expo/xcpretty": "npm:^4.3.0"
    "@react-native/dev-middleware": "npm:0.74.85"
    "@urql/core": "npm:2.3.6"
    "@urql/exchange-retry": "npm:0.3.0"
    accepts: "npm:^1.3.8"
    arg: "npm:5.0.2"
    better-opn: "npm:~3.0.2"
    bplist-creator: "npm:0.0.7"
    bplist-parser: "npm:^0.3.1"
    cacache: "npm:^18.0.2"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.3.0"
    connect: "npm:^3.7.0"
    debug: "npm:^4.3.4"
    env-editor: "npm:^0.4.1"
    fast-glob: "npm:^3.3.2"
    find-yarn-workspace-root: "npm:~2.0.0"
    form-data: "npm:^3.0.1"
    freeport-async: "npm:2.0.0"
    fs-extra: "npm:~8.1.0"
    getenv: "npm:^1.0.0"
    glob: "npm:^7.1.7"
    graphql: "npm:15.8.0"
    graphql-tag: "npm:^2.10.1"
    https-proxy-agent: "npm:^5.0.1"
    internal-ip: "npm:4.3.0"
    is-docker: "npm:^2.0.0"
    is-wsl: "npm:^2.1.1"
    js-yaml: "npm:^3.13.1"
    json-schema-deref-sync: "npm:^0.13.0"
    lodash.debounce: "npm:^4.0.8"
    md5hex: "npm:^1.0.0"
    minimatch: "npm:^3.0.4"
    node-fetch: "npm:^2.6.7"
    node-forge: "npm:^1.3.1"
    npm-package-arg: "npm:^7.0.0"
    open: "npm:^8.3.0"
    ora: "npm:3.4.0"
    picomatch: "npm:^3.0.1"
    pretty-bytes: "npm:5.6.0"
    progress: "npm:2.0.3"
    prompts: "npm:^2.3.2"
    qrcode-terminal: "npm:0.11.0"
    require-from-string: "npm:^2.0.2"
    requireg: "npm:^0.2.2"
    resolve: "npm:^1.22.2"
    resolve-from: "npm:^5.0.0"
    resolve.exports: "npm:^2.0.2"
    semver: "npm:^7.6.0"
    send: "npm:^0.18.0"
    slugify: "npm:^1.3.4"
    source-map-support: "npm:~0.5.21"
    stacktrace-parser: "npm:^0.1.10"
    structured-headers: "npm:^0.4.1"
    tar: "npm:^6.0.5"
    temp-dir: "npm:^2.0.0"
    tempy: "npm:^0.7.1"
    terminal-link: "npm:^2.1.1"
    text-table: "npm:^0.2.0"
    url-join: "npm:4.0.0"
    wrap-ansi: "npm:^7.0.0"
    ws: "npm:^8.12.1"
  bin:
    expo-internal: build/bin/cli
  checksum: 10c0/cbf19c7cdf832d10187fdfeb68b44ec46dbcdc77eca99732819d1a16ff3c6985237880c41e878cd98533726724a86fc305eb699696b8a2e0ed8abcb43bab29c2
  languageName: node
  linkType: hard

"@expo/code-signing-certificates@npm:0.0.5":
  version: 0.0.5
  resolution: "@expo/code-signing-certificates@npm:0.0.5"
  dependencies:
    node-forge: "npm:^1.2.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/98c908c54f92d6782ae01fef47dd858140dc6013e5376ee3faf9b243327f2b16279441fec171cbde45d0e3ebd0bf72db57b4d4c2a0c4f952285b0b377b2b356b
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:8.0.11, @expo/config-plugins@npm:~8.0.8":
  version: 8.0.11
  resolution: "@expo/config-plugins@npm:8.0.11"
  dependencies:
    "@expo/config-types": "npm:^51.0.3"
    "@expo/json-file": "npm:~8.3.0"
    "@expo/plist": "npm:^0.1.0"
    "@expo/sdk-runtime-versions": "npm:^1.0.0"
    chalk: "npm:^4.1.2"
    debug: "npm:^4.3.1"
    find-up: "npm:~5.0.0"
    getenv: "npm:^1.0.0"
    glob: "npm:7.1.6"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.5.4"
    slash: "npm:^3.0.0"
    slugify: "npm:^1.6.6"
    xcode: "npm:^3.0.1"
    xml2js: "npm:0.6.0"
  checksum: 10c0/0dac5afd845c050334afb816fca447df96e27fc004bd99873e2f8ffed0ad8e7fc2e9bbb2877589b5ea6fc73c35144dc7bf174ca46cacfa14b1baf93a094e350d
  languageName: node
  linkType: hard

"@expo/config-types@npm:^51.0.3":
  version: 51.0.3
  resolution: "@expo/config-types@npm:51.0.3"
  checksum: 10c0/bd87a729da985b0097ab29367c0473a2bced5ff211d416f342729e1b2631a7c00d62878e05cdee49ece7e3b65d3296957917f24380d57e2faef2cf220194fdec
  languageName: node
  linkType: hard

"@expo/config@npm:9.0.4, @expo/config@npm:~9.0.0, @expo/config@npm:~9.0.0-beta.0":
  version: 9.0.4
  resolution: "@expo/config@npm:9.0.4"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    "@expo/config-plugins": "npm:~8.0.8"
    "@expo/config-types": "npm:^51.0.3"
    "@expo/json-file": "npm:^8.3.0"
    getenv: "npm:^1.0.0"
    glob: "npm:7.1.6"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    slugify: "npm:^1.3.4"
    sucrase: "npm:3.34.0"
  checksum: 10c0/3ff42bed172be89652e0e3b1171e7acb20b28314b72cac3b26b1da53b0d6753485502ff1786e05c02ede312e3ff839732c2637ff828bdfe58bcbb91c1669f297
  languageName: node
  linkType: hard

"@expo/devcert@npm:^1.0.0":
  version: 1.2.0
  resolution: "@expo/devcert@npm:1.2.0"
  dependencies:
    "@expo/sudo-prompt": "npm:^9.3.1"
    debug: "npm:^3.1.0"
    glob: "npm:^10.4.2"
  checksum: 10c0/3d6a1ce44918c2e5be3bb89d25cfc80551623e4fe5004d4eb29d1edc8edd676258345e64d2aefe56188bc5d4b33e2b7e733a108b2be225af1f90ca86d7170069
  languageName: node
  linkType: hard

"@expo/env@npm:~0.3.0":
  version: 0.3.0
  resolution: "@expo/env@npm:0.3.0"
  dependencies:
    chalk: "npm:^4.0.0"
    debug: "npm:^4.3.4"
    dotenv: "npm:~16.4.5"
    dotenv-expand: "npm:~11.0.6"
    getenv: "npm:^1.0.0"
  checksum: 10c0/cb8ee6406083c41b55f58da9bc7ab4b1e38227220d72aa22b5f11b5d22756014c61c327f3f214dcd8eb4b6f1a10162745a921ebe7b0d8d841f40a6ece320f07b
  languageName: node
  linkType: hard

"@expo/image-utils@npm:^0.5.0":
  version: 0.5.1
  resolution: "@expo/image-utils@npm:0.5.1"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    fs-extra: "npm:9.0.0"
    getenv: "npm:^1.0.0"
    jimp-compact: "npm:0.16.1"
    node-fetch: "npm:^2.6.0"
    parse-png: "npm:^2.1.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    tempy: "npm:0.3.0"
  checksum: 10c0/611a0c4833abb9fd5a4c65265d85c032271746b806700c8e4df24cfa427fe666722a74f4acfa81779497a726221c3539599570e4a2817f5b8b29310c37e16ed5
  languageName: node
  linkType: hard

"@expo/json-file@npm:^8.3.0, @expo/json-file@npm:~8.3.0":
  version: 8.3.3
  resolution: "@expo/json-file@npm:8.3.3"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    json5: "npm:^2.2.2"
    write-file-atomic: "npm:^2.3.0"
  checksum: 10c0/3b1b593a2fe6cb297713fbe2d1002bbc8d469fc55219343bffcce1b1abe941aace1b239d0afc1a3cf15b7ceed91e8da4ca36cb83b586f3bf9f05856e1ad560d3
  languageName: node
  linkType: hard

"@expo/json-file@npm:^9.1.5":
  version: 9.1.5
  resolution: "@expo/json-file@npm:9.1.5"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    json5: "npm:^2.2.3"
  checksum: 10c0/989e3aa6d3e31a7f499d7979c6062694f2bc1fe1a4bc81b64aff74c39f27ed5f52098861897236cdc26b86186062560f3191814a2e8ff5b821a74a71d617f135
  languageName: node
  linkType: hard

"@expo/metro-config@npm:0.18.11":
  version: 0.18.11
  resolution: "@expo/metro-config@npm:0.18.11"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.5"
    "@babel/parser": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    "@expo/config": "npm:~9.0.0-beta.0"
    "@expo/env": "npm:~0.3.0"
    "@expo/json-file": "npm:~8.3.0"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.1.0"
    debug: "npm:^4.3.2"
    find-yarn-workspace-root: "npm:~2.0.0"
    fs-extra: "npm:^9.1.0"
    getenv: "npm:^1.0.0"
    glob: "npm:^7.2.3"
    jsc-safe-url: "npm:^0.2.4"
    lightningcss: "npm:~1.19.0"
    postcss: "npm:~8.4.32"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/775990b6e58e8f1a4bb7215bfa341315e599df8a6ca4f5c0cc12122ac953703627cbe5ca3339ee61e1bc1b91bfbe1c4ae5999b37fe68e2fa65cbbd9889a7536c
  languageName: node
  linkType: hard

"@expo/osascript@npm:^2.0.31":
  version: 2.2.5
  resolution: "@expo/osascript@npm:2.2.5"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    exec-async: "npm:^2.2.0"
  checksum: 10c0/1fd6d0ebb43eb09d57db02b4053dd4b1aa5ba341aebbe10c1b39afa03c4724c77778e2e5ed932fe2ff24724ff73f464789778ca9e964aea6dfe3481f3ffbf570
  languageName: node
  linkType: hard

"@expo/package-manager@npm:^1.5.0":
  version: 1.8.6
  resolution: "@expo/package-manager@npm:1.8.6"
  dependencies:
    "@expo/json-file": "npm:^9.1.5"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    npm-package-arg: "npm:^11.0.0"
    ora: "npm:^3.4.0"
    resolve-workspace-root: "npm:^2.0.0"
  checksum: 10c0/2d2860016ce15a0c7c6d99bd3df28b0250ce277eae0120696c84ee3edae644524baa43be0874fe7a4796c39ff7ba9de23337360ab2e5fc1501e5da411345143f
  languageName: node
  linkType: hard

"@expo/plist@npm:^0.1.0":
  version: 0.1.3
  resolution: "@expo/plist@npm:0.1.3"
  dependencies:
    "@xmldom/xmldom": "npm:~0.7.7"
    base64-js: "npm:^1.2.3"
    xmlbuilder: "npm:^14.0.0"
  checksum: 10c0/134315260a7828bc1ce4563e2af67499b498feae46c39c5c2cab9d72082402a42d3b7575f13e269022bcf3c28668948ea960dd4943bd38f52f9c01154317aac5
  languageName: node
  linkType: hard

"@expo/prebuild-config@npm:7.0.9":
  version: 7.0.9
  resolution: "@expo/prebuild-config@npm:7.0.9"
  dependencies:
    "@expo/config": "npm:~9.0.0-beta.0"
    "@expo/config-plugins": "npm:~8.0.8"
    "@expo/config-types": "npm:^51.0.3"
    "@expo/image-utils": "npm:^0.5.0"
    "@expo/json-file": "npm:^8.3.0"
    "@react-native/normalize-colors": "npm:0.74.85"
    debug: "npm:^4.3.1"
    fs-extra: "npm:^9.0.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    xml2js: "npm:0.6.0"
  peerDependencies:
    expo-modules-autolinking: ">=0.8.1"
  checksum: 10c0/aa1ebd22bdcaaecbe42526c00f6109007531daa10db80224394f030dc87c8b238d46ccac4130ccac32540a448a5f3b637a84e326e66ee6425f18b63a2f32cb7b
  languageName: node
  linkType: hard

"@expo/rudder-sdk-node@npm:1.1.1":
  version: 1.1.1
  resolution: "@expo/rudder-sdk-node@npm:1.1.1"
  dependencies:
    "@expo/bunyan": "npm:^4.0.0"
    "@segment/loosely-validate-event": "npm:^2.0.0"
    fetch-retry: "npm:^4.1.1"
    md5: "npm:^2.2.1"
    node-fetch: "npm:^2.6.1"
    remove-trailing-slash: "npm:^0.1.0"
    uuid: "npm:^8.3.2"
  checksum: 10c0/1a13089bc2b8d437c45be64051f6e819966a7b8875bab4587c34c0841374a7b00ade7b76fa09d961a1e31343d5b3423f3a5f65658dcc883fd8b3dbddc53a8f7d
  languageName: node
  linkType: hard

"@expo/sdk-runtime-versions@npm:^1.0.0":
  version: 1.0.0
  resolution: "@expo/sdk-runtime-versions@npm:1.0.0"
  checksum: 10c0/f80ae78a294daf396f3eff2eb412948ced5501395a6d3b88058866da9c5135dbacbb2804f8d062222e7452159a61eebefd2f548a2939f539f0f0efe8145588a2
  languageName: node
  linkType: hard

"@expo/spawn-async@npm:^1.7.2":
  version: 1.7.2
  resolution: "@expo/spawn-async@npm:1.7.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
  checksum: 10c0/0548c4e95ee39393c2f3919bc605f21eba4f0a8ba66fa82fbbc4b1b624e0054526918489227b924f03af5bc156a011f39a2472c223c0d2237fb7afd8dedd5357
  languageName: node
  linkType: hard

"@expo/sudo-prompt@npm:^9.3.1":
  version: 9.3.2
  resolution: "@expo/sudo-prompt@npm:9.3.2"
  checksum: 10c0/032652bf1c3f326c9c194f336de5821b9ece9d48b22e3e277950d939fcd728c85459680a9771705904d375f128221cca2e1e91c5d7a85cf3c07fe6f88c361e9d
  languageName: node
  linkType: hard

"@expo/vector-icons@npm:^14.0.3":
  version: 14.1.0
  resolution: "@expo/vector-icons@npm:14.1.0"
  peerDependencies:
    expo-font: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/f1dcea2c43c0808f48d1953395c6f8025ae5e811648e86b79158492c9ef8af7a40781e42844dfb1434242a08fcf6ab14886825eb2c79bad2a792aebd1eb5077c
  languageName: node
  linkType: hard

"@expo/xcpretty@npm:^4.3.0":
  version: 4.3.2
  resolution: "@expo/xcpretty@npm:4.3.2"
  dependencies:
    "@babel/code-frame": "npm:7.10.4"
    chalk: "npm:^4.1.0"
    find-up: "npm:^5.0.0"
    js-yaml: "npm:^4.1.0"
  bin:
    excpretty: build/cli.js
  checksum: 10c0/e524817b2e42fb8c8914fca7e8f7c2f723f4f6d338a57b7ae97cd3e76da8108af63a22d4c7dc2e96a192a248a242f6e0f8056f0ca53bc4fb5cd2e5ae428e0891
  languageName: node
  linkType: hard

"@formatjs/ecma402-abstract@npm:1.17.2":
  version: 1.17.2
  resolution: "@formatjs/ecma402-abstract@npm:1.17.2"
  dependencies:
    "@formatjs/intl-localematcher": "npm:0.4.2"
    tslib: "npm:^2.4.0"
  checksum: 10c0/7086962b6f6fd517188e9640e8439062e125e7bd60852ae91ceadb259fceaa3f73f7403a77411a7c82b1ae60be4bf4b27f793acc4059214adc91b00682d880fe
  languageName: node
  linkType: hard

"@formatjs/fast-memoize@npm:2.2.0":
  version: 2.2.0
  resolution: "@formatjs/fast-memoize@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/ae88c5a93b96235aba4bd9b947d0310d2ec013687a99133413361b24122b5cdea8c9bf2e04a4a2a8b61f1f4ee5419ef6416ca4796554226b5050e05a9ce6ef49
  languageName: node
  linkType: hard

"@formatjs/icu-messageformat-parser@npm:2.7.0":
  version: 2.7.0
  resolution: "@formatjs/icu-messageformat-parser@npm:2.7.0"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.17.2"
    "@formatjs/icu-skeleton-parser": "npm:1.6.2"
    tslib: "npm:^2.4.0"
  checksum: 10c0/f671d3dfcfa8ada17d14388f21be4cf1c535cbad8aba9cd6a3132d3120424cb7fb090f67b27e44ffbd0c7d4bba9f20e76b74cfca87f4ab945939e7ea3acb878c
  languageName: node
  linkType: hard

"@formatjs/icu-skeleton-parser@npm:1.6.2":
  version: 1.6.2
  resolution: "@formatjs/icu-skeleton-parser@npm:1.6.2"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.17.2"
    tslib: "npm:^2.4.0"
  checksum: 10c0/2a2a56f49a15e8122b37237d5e09a817e01149ae353e1b5fe8721d1789dbaee85995c897d3aa9e5b400e3ee05b5fd4c0721b3ad49b1d128954dfed873a793153
  languageName: node
  linkType: hard

"@formatjs/intl-localematcher@npm:0.4.2":
  version: 0.4.2
  resolution: "@formatjs/intl-localematcher@npm:0.4.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/2521fa48a95a80e3bedc0d444fb2ef67e1215e0bf9e6d16020c4a22af6973849a71c7a29a10cb74fc67b818967e9f8672062760e808e70873132277830e0ec67
  languageName: node
  linkType: hard

"@graphql-typed-document-node/core@npm:^3.1.0":
  version: 3.2.0
  resolution: "@graphql-typed-document-node/core@npm:3.2.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10c0/94e9d75c1f178bbae8d874f5a9361708a3350c8def7eaeb6920f2c820e82403b7d4f55b3735856d68e145e86c85cbfe2adc444fdc25519cd51f108697e99346c
  languageName: node
  linkType: hard

"@hapi/hoek@npm:^9.0.0, @hapi/hoek@npm:^9.3.0":
  version: 9.3.0
  resolution: "@hapi/hoek@npm:9.3.0"
  checksum: 10c0/a096063805051fb8bba4c947e293c664b05a32b47e13bc654c0dd43813a1cec993bdd8f29ceb838020299e1d0f89f68dc0d62a603c13c9cc8541963f0beca055
  languageName: node
  linkType: hard

"@hapi/topo@npm:^5.1.0":
  version: 5.1.0
  resolution: "@hapi/topo@npm:5.1.0"
  dependencies:
    "@hapi/hoek": "npm:^9.0.0"
  checksum: 10c0/b16b06d9357947149e032bdf10151eb71aea8057c79c4046bf32393cb89d0d0f7ca501c40c0f7534a5ceca078de0700d2257ac855c15e59fe4e00bba2f25c86f
  languageName: node
  linkType: hard

"@internationalized/date@npm:^3.5.0":
  version: 3.5.0
  resolution: "@internationalized/date@npm:3.5.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/20e7af377fcc61d5c780bda33e5e2b5fa3d31a7bb5c71f488de1bdd364bd4585812edd18d49fd427900397383774c85dd842bd84147e82fbf91f178ee2fbb314
  languageName: node
  linkType: hard

"@internationalized/message@npm:^3.1.1":
  version: 3.1.1
  resolution: "@internationalized/message@npm:3.1.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
    intl-messageformat: "npm:^10.1.0"
  checksum: 10c0/e778de8567e424e00c52f9b69eb65cea817664803add4bc4f76eb0248d12a4a7eaedbc15a290cabe90a070826d25a93438dea99a1bee796deb024b78cd694a92
  languageName: node
  linkType: hard

"@internationalized/number@npm:^3.3.0":
  version: 3.3.0
  resolution: "@internationalized/number@npm:3.3.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/2990d0c505cd83f681937821b6c0f7079f3fd624d3a28a761047ce7f79a1c266d07f6eac80786d83807f45ed48492a4c20d83297f3160be93e85f8bf1dea6701
  languageName: node
  linkType: hard

"@internationalized/string@npm:^3.1.1":
  version: 3.1.1
  resolution: "@internationalized/string@npm:3.1.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/c004037c79b6a42f87f7c15d50a9abc5da796af5f74717569a8576eb4ae9b87aed37fc8eef0d658e65540f21fdbba160dcba2128f78a94b8acd31fc72c966014
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@isaacs/ttlcache@npm:^1.4.1":
  version: 1.4.1
  resolution: "@isaacs/ttlcache@npm:1.4.1"
  checksum: 10c0/6921de516917b02673a58e543c2b06fd04237cbf6d089ca22d6e98defa4b1e9a48258cb071d6b581284bb497bea687320788830541511297eecbe6e93a665bbf
  languageName: node
  linkType: hard

"@jest/create-cache-key-function@npm:^29.6.3":
  version: 29.7.0
  resolution: "@jest/create-cache-key-function@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
  checksum: 10c0/5c47ef62205264adf77b1ff26b969ce9fe84920b8275c3c5e83f4236859d6ae5e4e7027af99eef04a8e334c4e424d44af3e167972083406070aca733ac2a2795
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/c7b1b40c618f8baf4d00609022d2afa086d9c6acc706f303a70bb4b67275868f620ad2e1a9efc5edd418906157337cce50589a627a6400bbdf117d351b91ef86
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/cf0a8bcda801b28dc2e2b2ba36302200ee8104a45ad7a21e6c234148932f826cb3bc57c8df3b7b815aeea0861d7b6ca6f0d4778f93b9219398ef28749e03595c
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/b329e89cd5f20b9278ae1233df74016ebf7b385e0d14b9f4c1ad18d096c4c19d1e687aa113a9c976b16ec07f021ae53dea811fb8c1248a50ac34fbe009fdf6be
  languageName: node
  linkType: hard

"@jest/types@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/types@npm:24.9.0"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^1.1.1"
    "@types/yargs": "npm:^13.0.0"
  checksum: 10c0/990b03f5e27de292a7fea6b12cd87256dd281263afe37020cad5dceb0b775945a528bafdbc2e41bf8a29c346f94a7aa5580517c5c65a2b33f245f43d3b9b4694
  languageName: node
  linkType: hard

"@jest/types@npm:^26.6.2":
  version: 26.6.2
  resolution: "@jest/types@npm:26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^15.0.0"
    chalk: "npm:^4.0.0"
  checksum: 10c0/5b9b957f38a002895eb04bbb8c3dda6fccce8e2551f3f44b02f1f43063a78e8bedce73cd4330b53ede00ae005de5cd805982fbb2ec6ab9feacf96344240d5db2
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10c0/ea4e493dd3fb47933b8ccab201ae573dcc451f951dc44ed2a86123cd8541b82aa9d2b1031caf9b1080d6673c517e2dcc25a44b2dc4f3fbc37bfc965d444888c0
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/32f771ae2467e4d440be609581f7338d786d3d621bac3469e943b9d6d116c23c4becb36f84898a92bbf2f3c0511365c54a945a3b86a83141547a2a360a5ec0c7
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.1
  resolution: "@jridgewell/resolve-uri@npm:3.1.1"
  checksum: 10c0/0dbc9e29bc640bbbdc5b9876d2859c69042bfcf1423c1e6421bcca53e826660bff4e41c7d4bcb8dbea696404231a6f902f76ba41835d049e20f2dd6cffb713bf
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.10
  resolution: "@jridgewell/source-map@npm:0.3.10"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/cf6a51808bc710eb91a9e6c5e250c1af5714299c8de3db2b74e273a27ba7313f37c198ba332a512b7657fa23fed125c0147bfb1b925cadc9697a89cebecad0d8
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 10c0/c5aab3e6362a8dd94ad80ab90845730c825fc4c8d9cf07ebca7a2eb8a832d155d62558800fc41d42785f989ddbb21db6df004d1786e8ecb65e428ab8dff71309
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25, @jridgewell/trace-mapping@npm:^0.3.28, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/fb547ba31658c4d74eb17e7389f4908bf7c44cef47acb4c5baa57289daf68e6fe53c639f41f751b3923aca67010501264f70e7b49978ad1f040294b22c37b333
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.1
  resolution: "@npmcli/fs@npm:3.1.1"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c37a5b4842bfdece3d14dfdb054f73fe15ed2d3da61b34ff76629fb5b1731647c49166fd2a8bf8b56fcfa51200382385ea8909a3cbecdad612310c114d3f6c99
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@react-aria/checkbox@npm:^3.2.1":
  version: 3.11.2
  resolution: "@react-aria/checkbox@npm:3.11.2"
  dependencies:
    "@react-aria/label": "npm:^3.7.2"
    "@react-aria/toggle": "npm:^3.8.2"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/checkbox": "npm:^3.5.1"
    "@react-stately/toggle": "npm:^3.6.3"
    "@react-types/checkbox": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/a924a564e6295ecdc09351aa91c1b401ed52168530c4ac7209bb293a894ba2438039f36a0ac914c3efdec98e417e67333a63f450b745267ed581980c4e968810
  languageName: node
  linkType: hard

"@react-aria/combobox@npm:^3.0.0-alpha.1":
  version: 3.7.1
  resolution: "@react-aria/combobox@npm:3.7.1"
  dependencies:
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/listbox": "npm:^3.11.1"
    "@react-aria/live-announcer": "npm:^3.3.1"
    "@react-aria/menu": "npm:^3.11.1"
    "@react-aria/overlays": "npm:^3.18.1"
    "@react-aria/selection": "npm:^3.17.1"
    "@react-aria/textfield": "npm:^3.12.2"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/combobox": "npm:^3.7.1"
    "@react-stately/layout": "npm:^3.13.3"
    "@react-types/button": "npm:^3.9.0"
    "@react-types/combobox": "npm:^3.8.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/e332fc7d5151c2d6f17d57fd988ce2ca8a0734d668a08b16af36a3a2d0d14ee355e60e3d0ead24b287df6aa6085ee201901f1be1763a11a8c4929cf1a2558706
  languageName: node
  linkType: hard

"@react-aria/focus@npm:^3.14.3, @react-aria/focus@npm:^3.2.3":
  version: 3.14.3
  resolution: "@react-aria/focus@npm:3.14.3"
  dependencies:
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^1.1.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/2a5f80d5a41d150770466a1e1a7114345cdce3d68f38ab7f3c7524d544a8a5f40416e949825c781c60027eed206d533bff6cefccb02348976fa6aa8676404977
  languageName: node
  linkType: hard

"@react-aria/i18n@npm:^3.2.0, @react-aria/i18n@npm:^3.3.0, @react-aria/i18n@npm:^3.8.4":
  version: 3.8.4
  resolution: "@react-aria/i18n@npm:3.8.4"
  dependencies:
    "@internationalized/date": "npm:^3.5.0"
    "@internationalized/message": "npm:^3.1.1"
    "@internationalized/number": "npm:^3.3.0"
    "@internationalized/string": "npm:^3.1.1"
    "@react-aria/ssr": "npm:^3.8.0"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/5420e9a7a55bb81dbde6225ad6023d0e0d379e8689bb3e1c21a13fc1fe9d11f4061452821517f11f55493451124f6f0cb32fa0181e018823b33c0abbde8df513
  languageName: node
  linkType: hard

"@react-aria/interactions@npm:^3.19.1, @react-aria/interactions@npm:^3.3.2":
  version: 3.19.1
  resolution: "@react-aria/interactions@npm:3.19.1"
  dependencies:
    "@react-aria/ssr": "npm:^3.8.0"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/f850f5b20028e4629987fcf378b940e98a523eb111a26dde09c4167dda236c1e46225e3f723f64301d2578f1f97d423c577ac03e92a1abffa5686bc249b1986f
  languageName: node
  linkType: hard

"@react-aria/label@npm:^3.1.1, @react-aria/label@npm:^3.7.2":
  version: 3.7.2
  resolution: "@react-aria/label@npm:3.7.2"
  dependencies:
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/label": "npm:^3.8.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/861deaaf8cbadb42deaf9944efedc4e60d03f63ad75eb96d4a7ad09397c9315eb134d06fcdc1c9083e153d158a25982ee5637eb53c931b97379f0cd6a876c4d4
  languageName: node
  linkType: hard

"@react-aria/listbox@npm:^3.11.1, @react-aria/listbox@npm:^3.2.4":
  version: 3.11.1
  resolution: "@react-aria/listbox@npm:3.11.1"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/label": "npm:^3.7.2"
    "@react-aria/selection": "npm:^3.17.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/list": "npm:^3.10.0"
    "@react-types/listbox": "npm:^3.4.5"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/16a6e7d083032d916b585227bda3e476e2daddcabbf471b5fa349bae857d00aeebb780079c74d877b05886c9ac8f743275d7d2abb896f194ffdef93911dbb7a1
  languageName: node
  linkType: hard

"@react-aria/live-announcer@npm:^3.0.0-alpha.0, @react-aria/live-announcer@npm:^3.3.1":
  version: 3.3.1
  resolution: "@react-aria/live-announcer@npm:3.3.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/c105324714f8d2a2f0a4837913e9dc59cf39405659e6ec65997c591cc4d3e43d39f1c65ff3a8150e86c067f76dd39177332d2dc8d8d5d75a02bbd331be4499b3
  languageName: node
  linkType: hard

"@react-aria/menu@npm:^3.11.1":
  version: 3.11.1
  resolution: "@react-aria/menu@npm:3.11.1"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/overlays": "npm:^3.18.1"
    "@react-aria/selection": "npm:^3.17.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/menu": "npm:^3.5.6"
    "@react-stately/tree": "npm:^3.7.3"
    "@react-types/button": "npm:^3.9.0"
    "@react-types/menu": "npm:^3.9.5"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/8771af1273fbe81d872bf8071650eef7ea6cef0d7be1d567af97465c4398da989a8a97fd711de357f8ea9a71f2331a73d1e6594de149c240d742b7329c79cc4f
  languageName: node
  linkType: hard

"@react-aria/overlays@npm:^3.18.1, @react-aria/overlays@npm:^3.7.0":
  version: 3.18.1
  resolution: "@react-aria/overlays@npm:3.18.1"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/ssr": "npm:^3.8.0"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-aria/visually-hidden": "npm:^3.8.6"
    "@react-stately/overlays": "npm:^3.6.3"
    "@react-types/button": "npm:^3.9.0"
    "@react-types/overlays": "npm:^3.8.3"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/4b8ea7b93303a24514e64e32537cdd4df6c9f0eb60dc325a51eaeff796b69ac8b78757e1207a7939ca238c6394c246b910a94906b629332fdb2442da39e49585
  languageName: node
  linkType: hard

"@react-aria/radio@npm:^3.1.2":
  version: 3.8.2
  resolution: "@react-aria/radio@npm:3.8.2"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/label": "npm:^3.7.2"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/radio": "npm:^3.9.1"
    "@react-types/radio": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/d6c07bfe41e522bb14d5a869eba17d5d9dc18bf959e62cfc8d2e43cd97f78bd80ad689c4c945bfd8c39772e2c6fb7364d26cf2e7b457ec5bcbe0c25f03f6dfe9
  languageName: node
  linkType: hard

"@react-aria/selection@npm:^3.17.1, @react-aria/selection@npm:^3.3.1, @react-aria/selection@npm:^3.3.2":
  version: 3.17.1
  resolution: "@react-aria/selection@npm:3.17.1"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/dcf12595551b3f0c96d0b66ed09d2d0a08cbe6a1bae4074d08e852456e9f77a25d70d2c9df728017c983b5d7151d7ab1196ea546d6f7fac0a1005d30de4d7380
  languageName: node
  linkType: hard

"@react-aria/slider@npm:^3.0.1":
  version: 3.7.2
  resolution: "@react-aria/slider@npm:3.7.2"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/label": "npm:^3.7.2"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/radio": "npm:^3.9.1"
    "@react-stately/slider": "npm:^3.4.4"
    "@react-types/radio": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/slider": "npm:^3.6.2"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/bc93674b71184440f7f7050a7cf8e11d3965279e245a63d0244011459fc3f895b2f1eb60303f7098d965d3f946500a3eff53b4ab1204be1c679e2802235669ec
  languageName: node
  linkType: hard

"@react-aria/ssr@npm:^3.0.1, @react-aria/ssr@npm:^3.8.0":
  version: 3.8.0
  resolution: "@react-aria/ssr@npm:3.8.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/c218d93f4be967da97aec354fc6a7c486ac7b04f86ebed5853165ead444b3d94761b239a809fbbdf29bb4ed9ea99c2a55131910a2bb483a94bb7e0321d8924b7
  languageName: node
  linkType: hard

"@react-aria/tabs@npm:3.0.0-alpha.2":
  version: 3.0.0-alpha.2
  resolution: "@react-aria/tabs@npm:3.0.0-alpha.2"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-aria/i18n": "npm:^3.2.0"
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/selection": "npm:^3.3.1"
    "@react-aria/utils": "npm:^3.4.1"
    "@react-stately/list": "npm:^3.2.2"
    "@react-stately/tabs": "npm:3.0.0-alpha.0"
    "@react-types/shared": "npm:^3.2.1"
    "@react-types/tabs": "npm:3.0.0-alpha.2"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/0d42de094f3f718a7efa4a94c7c46b70f93a2ad905bcd999fd2f242a4b51f7dc0349b53850176a67b88345e0700e8e08ac5906901e6aa67960abb2d259863b1b
  languageName: node
  linkType: hard

"@react-aria/textfield@npm:^3.12.2":
  version: 3.12.2
  resolution: "@react-aria/textfield@npm:3.12.2"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/label": "npm:^3.7.2"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/textfield": "npm:^3.8.1"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/09fed22cb26426863e9dd13b2bb3539174461b20ab012359e8dfe51ffc8518c44bbcde6009670a54465666f78035a63baf5c692a1755bb78d9a52f965dc55bd3
  languageName: node
  linkType: hard

"@react-aria/toggle@npm:^3.8.2":
  version: 3.8.2
  resolution: "@react-aria/toggle@npm:3.8.2"
  dependencies:
    "@react-aria/focus": "npm:^3.14.3"
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/toggle": "npm:^3.6.3"
    "@react-types/checkbox": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/switch": "npm:^3.4.2"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/d6d62f17139a00324f82b59b4397105576f901e7825faaf4fc175c79b6153528f8f5bdc5282bcdb053651df67d2d5ad81bc9b8f8f3e767cd2c93ff64cec7b97f
  languageName: node
  linkType: hard

"@react-aria/utils@npm:^3.21.1, @react-aria/utils@npm:^3.3.0, @react-aria/utils@npm:^3.4.1, @react-aria/utils@npm:^3.6.0":
  version: 3.21.1
  resolution: "@react-aria/utils@npm:3.21.1"
  dependencies:
    "@react-aria/ssr": "npm:^3.8.0"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^1.1.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/236d998297090439d32f20b0818c479681144776f14ac3a452ea3ca4e42fac1982e15dd8f7e7068e3e6e8e2021161df37986727229e705e70e5c82771ff002b1
  languageName: node
  linkType: hard

"@react-aria/visually-hidden@npm:^3.2.1, @react-aria/visually-hidden@npm:^3.8.6":
  version: 3.8.6
  resolution: "@react-aria/visually-hidden@npm:3.8.6"
  dependencies:
    "@react-aria/interactions": "npm:^3.19.1"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^1.1.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/c6df3e4b2353907e02278de17b284ceb11718dd60e0a774e362b8163f99ada6731e84271428eed7df982a956f6964676594e198f4e03d9493c2554bb79f03608
  languageName: node
  linkType: hard

"@react-native-aria/button@npm:^0.2.4":
  version: 0.2.5
  resolution: "@react-native-aria/button@npm:0.2.5"
  dependencies:
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/interactions": "npm:^0.2.11"
    "@react-stately/toggle": "npm:^3.2.1"
    "@react-types/checkbox": "npm:^3.2.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/4c333d49924993abc8ded622d0fda5ff6cd777109f5a4bbe4ee255958872cc184289cc2a46e6802b46f053c4dbf26bfd4e0767cb9483f0556608c481362c2265
  languageName: node
  linkType: hard

"@react-native-aria/checkbox@npm:^0.2.3":
  version: 0.2.6
  resolution: "@react-native-aria/checkbox@npm:0.2.6"
  dependencies:
    "@react-aria/checkbox": "npm:^3.2.1"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/toggle": "npm:^0.2.6"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-stately/toggle": "npm:^3.2.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/8e05b0ec09da607a45c41807367ff01c0d8624f000d88dd9d55698b13f1c1b91bbe0a7102e8523a284f20d079727f2d1a8b8748f9ca383d86a877dd1d724ba62
  languageName: node
  linkType: hard

"@react-native-aria/combobox@npm:^0.2.4-alpha.0":
  version: 0.2.5
  resolution: "@react-native-aria/combobox@npm:0.2.5"
  dependencies:
    "@react-aria/combobox": "npm:^3.0.0-alpha.1"
    "@react-aria/live-announcer": "npm:^3.0.0-alpha.0"
    "@react-aria/overlays": "npm:^3.7.0"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-types/button": "npm:^3.3.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/427ac20373c89356a38dc97861ed99430346930e485a2a37c9ec8ce8e02603d00c5b0e7699dbfd53d435e6ba9aa30f407302f16a39df753cd773902058cfff54
  languageName: node
  linkType: hard

"@react-native-aria/focus@npm:^0.2.6":
  version: 0.2.9
  resolution: "@react-native-aria/focus@npm:0.2.9"
  dependencies:
    "@react-aria/focus": "npm:^3.2.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/1bbfdcfd584ac9bad44ca4c128501e0727a6616012747cfff344f2230a7654a75c099cca8561fa558c897e25e077ec1acd6d4b4f1eed5bfcd5cb972fd350e7a7
  languageName: node
  linkType: hard

"@react-native-aria/interactions@npm:^0.2.11, @react-native-aria/interactions@npm:^0.2.2, @react-native-aria/interactions@npm:^0.2.7":
  version: 0.2.11
  resolution: "@react-native-aria/interactions@npm:0.2.11"
  dependencies:
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/utils": "npm:^0.2.10"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/9528716ba335676eb189ed365679714ae4fc5f2c94d9d2c0de2129c9fcd2ce5e795539095b1ebfd0c3bc712e343936bdb3c88ac3fb5d6305ec9c9e424e4ad7f2
  languageName: node
  linkType: hard

"@react-native-aria/listbox@npm:^0.2.4-alpha.3":
  version: 0.2.4
  resolution: "@react-native-aria/listbox@npm:0.2.4"
  dependencies:
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/label": "npm:^3.1.1"
    "@react-aria/listbox": "npm:^3.2.4"
    "@react-aria/selection": "npm:^3.3.2"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/interactions": "npm:^0.2.11"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-types/listbox": "npm:^3.1.1"
    "@react-types/shared": "npm:^3.4.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/08ceb4d3a9a782b002bce9c2e1385584a6b92f8390be92f414a1a0a8fed9052b2cefcd8c75a21b42264426df9acbf6f119f2d3d518a44bf85c73ae68dd61cb63
  languageName: node
  linkType: hard

"@react-native-aria/overlays@npm:^0.3.3":
  version: 0.3.10
  resolution: "@react-native-aria/overlays@npm:0.3.10"
  dependencies:
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/overlays": "npm:^3.7.0"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-stately/overlays": "npm:^3.1.1"
    "@react-types/overlays": "npm:^3.4.0"
    dom-helpers: "npm:^5.0.0"
  peerDependencies:
    react: "*"
    react-dom: "*"
    react-native: "*"
  checksum: 10c0/136293bf854cf98dce76ffa961d6ac3ed2a69b3c802d64fcd272a7b85bd341a43f9b163fa23e00353635147ddc5844c449d51d866a7adbdb0628459611cc423b
  languageName: node
  linkType: hard

"@react-native-aria/radio@npm:^0.2.4":
  version: 0.2.7
  resolution: "@react-native-aria/radio@npm:0.2.7"
  dependencies:
    "@react-aria/radio": "npm:^3.1.2"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/interactions": "npm:^0.2.11"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-stately/radio": "npm:^3.2.1"
    "@react-types/radio": "npm:^3.1.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/685988ff8e9861522c0d94036e0f0e29fa7bd1f95e16f39ef9a66c270fed0e1c50b11d2a26cbe27df0fa7cf1565ea4e064c323acd3d25a063bfbe02ad07964e6
  languageName: node
  linkType: hard

"@react-native-aria/slider@npm:^0.2.5-alpha.1":
  version: 0.2.9
  resolution: "@react-native-aria/slider@npm:0.2.9"
  dependencies:
    "@react-aria/focus": "npm:^3.2.3"
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/label": "npm:^3.1.1"
    "@react-aria/slider": "npm:^3.0.1"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-stately/slider": "npm:^3.0.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/a22d28992aa125bd26d04ea140fc048104f0626cf6e1f4a6edcb44020e10e8d8ffa3979f65bac6a7d6108ab6f853b0678e9e46c3ff1d381e0cf3de1d7d50e77c
  languageName: node
  linkType: hard

"@react-native-aria/tabs@npm:^0.2.7":
  version: 0.2.8
  resolution: "@react-native-aria/tabs@npm:0.2.8"
  dependencies:
    "@react-aria/selection": "npm:^3.3.1"
    "@react-aria/tabs": "npm:3.0.0-alpha.2"
    "@react-native-aria/interactions": "npm:^0.2.7"
    "@react-native-aria/utils": "npm:^0.2.7"
    "@react-stately/tabs": "npm:3.0.0-alpha.1"
    "@react-types/tabs": "npm:3.0.0-alpha.2"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/d462febeba271522fb15889fcc70445cc04f47aa94d07195842cd9c86585847695302d84a92239860d255b4e0ea4da5797d30831f29aa5a17924d4d84c58b0d9
  languageName: node
  linkType: hard

"@react-native-aria/toggle@npm:^0.2.6":
  version: 0.2.6
  resolution: "@react-native-aria/toggle@npm:0.2.6"
  dependencies:
    "@react-aria/focus": "npm:^3.2.3"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/interactions": "npm:^0.2.11"
    "@react-native-aria/utils": "npm:^0.2.10"
    "@react-stately/toggle": "npm:^3.2.1"
    "@react-types/checkbox": "npm:^3.2.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/958d8d4d5d49b57836a8b1b55b6bb9c699a29a891d907b0633d8a2480dfeee94504afb793d85815f72956e9c8542e2a19022ab44a41c0639301b251e6c7cc374
  languageName: node
  linkType: hard

"@react-native-aria/utils@npm:^0.2.10, @react-native-aria/utils@npm:^0.2.7, @react-native-aria/utils@npm:^0.2.8":
  version: 0.2.10
  resolution: "@react-native-aria/utils@npm:0.2.10"
  dependencies:
    "@react-aria/ssr": "npm:^3.0.1"
    "@react-aria/utils": "npm:^3.3.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/087117590917a0cabf2d20222d9b3db322ce722c5fde30c989e4741b7d02cfcedd9846bf30dc71a3e335e66e1049ad0b78b6bb66b7141ef2a8a70c3acb7e4e77
  languageName: node
  linkType: hard

"@react-native-async-storage/async-storage@npm:^1.23.1":
  version: 1.23.1
  resolution: "@react-native-async-storage/async-storage@npm:1.23.1"
  dependencies:
    merge-options: "npm:^3.0.4"
  peerDependencies:
    react-native: ^0.0.0-0 || >=0.60 <1.0
  checksum: 10c0/6cfdcab91f0e04f80f4aa91397eb4aedcd1124bf2a997cb730c7f2c70dad75d2aa0ca0f41576ed4e34544a1f79e090169f566366c32a89a41b74bf74bea1c1db
  languageName: node
  linkType: hard

"@react-native-community/cli-clean@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-clean@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    execa: "npm:^5.0.0"
    fast-glob: "npm:^3.3.2"
  checksum: 10c0/b40e4f0479c7ee419f1ce33f1d1278c2cf4d74fd9402852479a052f91ce56ee2e0b849e8d5cafea13f9fe246202823d5b2f8e1773eff610fcd84c1e190871624
  languageName: node
  linkType: hard

"@react-native-community/cli-config@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-config@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    cosmiconfig: "npm:^5.1.0"
    deepmerge: "npm:^4.3.0"
    fast-glob: "npm:^3.3.2"
    joi: "npm:^17.2.1"
  checksum: 10c0/f5635c1a02964d6ad36231acd1e0eda5bd0a47306939721bdc1f0c2258d989c3bcee1b5b77c5addb036d7846ec5c87fec72059e77f6b0d68815f079ef5d7d960
  languageName: node
  linkType: hard

"@react-native-community/cli-debugger-ui@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-debugger-ui@npm:13.6.9"
  dependencies:
    serve-static: "npm:^1.13.1"
  checksum: 10c0/9673c6ab96c84319e8b4b9df7b608fbf4bac1611e60b6363778aa0cec3ac2135d04212cc114122aee6007b3954054c5df27cc1fa59fe5edb2be2f0a4b9442afc
  languageName: node
  linkType: hard

"@react-native-community/cli-doctor@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-doctor@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-config": "npm:13.6.9"
    "@react-native-community/cli-platform-android": "npm:13.6.9"
    "@react-native-community/cli-platform-apple": "npm:13.6.9"
    "@react-native-community/cli-platform-ios": "npm:13.6.9"
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    command-exists: "npm:^1.2.8"
    deepmerge: "npm:^4.3.0"
    envinfo: "npm:^7.10.0"
    execa: "npm:^5.0.0"
    hermes-profile-transformer: "npm:^0.0.6"
    node-stream-zip: "npm:^1.9.1"
    ora: "npm:^5.4.1"
    semver: "npm:^7.5.2"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
    yaml: "npm:^2.2.1"
  checksum: 10c0/d39e5e31e58e849fa70c2430c83af6f1ec4468bd0995ebf944b2d9cdda008b82b347f15deef1aa026dbe4502691aabf9698f022c0739b980a73a07c3f6c090f0
  languageName: node
  linkType: hard

"@react-native-community/cli-hermes@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-hermes@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-platform-android": "npm:13.6.9"
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    hermes-profile-transformer: "npm:^0.0.6"
  checksum: 10c0/8e182570a65a1e57bde9dcaafe2d19741feac83a5e64f9c1828d0b24adcc78ea837720a12ad98769aab972647955f3b46c28b3ca2f465390c1ed44186d2d1b8e
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-android@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-platform-android@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    execa: "npm:^5.0.0"
    fast-glob: "npm:^3.3.2"
    fast-xml-parser: "npm:^4.2.4"
    logkitty: "npm:^0.7.1"
  checksum: 10c0/6083fe862e2166982b844d7b50d121ddf6e2a12c221b5e4ad950db3da4c2c6f92e030447eb301e254b7a43e593a6f4436dd34cad136d9cd8182517032264c409
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-apple@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-platform-apple@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-tools": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    execa: "npm:^5.0.0"
    fast-glob: "npm:^3.3.2"
    fast-xml-parser: "npm:^4.0.12"
    ora: "npm:^5.4.1"
  checksum: 10c0/3a9c900ebbb141083f5d7ebc2494a580010a9df73d2bd589f7707d23e6b3feacdf259c98c8cc774851e3fea21aab6366e255bf489c710dd5712b33c984f58812
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-ios@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-platform-ios@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-platform-apple": "npm:13.6.9"
  checksum: 10c0/e4d9b47a3ca945ab58c5087cbe6740f22b1f3ccf4e5d48250bfbb7d57d20026e8c1d5216618047f0ddf82a77b387910b6f2f7c73d5d4d44d0702096e380b4f96
  languageName: node
  linkType: hard

"@react-native-community/cli-server-api@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-server-api@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-debugger-ui": "npm:13.6.9"
    "@react-native-community/cli-tools": "npm:13.6.9"
    compression: "npm:^1.7.1"
    connect: "npm:^3.6.5"
    errorhandler: "npm:^1.5.1"
    nocache: "npm:^3.0.1"
    pretty-format: "npm:^26.6.2"
    serve-static: "npm:^1.13.1"
    ws: "npm:^6.2.2"
  checksum: 10c0/4061c25e66f5eaf5b397ae776feb4c5fcd1ee0ed4748e0694ba387870e67519145f255b69c2ea0583e8704580f3c7ba12d9e0181f80cc6f5e739c9c4f4f4e407
  languageName: node
  linkType: hard

"@react-native-community/cli-tools@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-tools@npm:13.6.9"
  dependencies:
    appdirsjs: "npm:^1.2.4"
    chalk: "npm:^4.1.2"
    execa: "npm:^5.0.0"
    find-up: "npm:^5.0.0"
    mime: "npm:^2.4.1"
    node-fetch: "npm:^2.6.0"
    open: "npm:^6.2.0"
    ora: "npm:^5.4.1"
    semver: "npm:^7.5.2"
    shell-quote: "npm:^1.7.3"
    sudo-prompt: "npm:^9.0.0"
  checksum: 10c0/a9b85cae49202aae81db33d3b62d06574c504bce634fbf0939dfa6ad6cae8f1b2728d4873fb5115023757a500280237992317c245e1b54dd96ca8c63c0f2582e
  languageName: node
  linkType: hard

"@react-native-community/cli-types@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli-types@npm:13.6.9"
  dependencies:
    joi: "npm:^17.2.1"
  checksum: 10c0/07be9711034265e6d602c659319ac3663adcc95b4633fd235ea6ce697681aaa3980c0bd13aa2e82e5f1309e21010619fef1e580e672f4649a7d4a91146c9a666
  languageName: node
  linkType: hard

"@react-native-community/cli@npm:13.6.9":
  version: 13.6.9
  resolution: "@react-native-community/cli@npm:13.6.9"
  dependencies:
    "@react-native-community/cli-clean": "npm:13.6.9"
    "@react-native-community/cli-config": "npm:13.6.9"
    "@react-native-community/cli-debugger-ui": "npm:13.6.9"
    "@react-native-community/cli-doctor": "npm:13.6.9"
    "@react-native-community/cli-hermes": "npm:13.6.9"
    "@react-native-community/cli-server-api": "npm:13.6.9"
    "@react-native-community/cli-tools": "npm:13.6.9"
    "@react-native-community/cli-types": "npm:13.6.9"
    chalk: "npm:^4.1.2"
    commander: "npm:^9.4.1"
    deepmerge: "npm:^4.3.0"
    execa: "npm:^5.0.0"
    find-up: "npm:^4.1.0"
    fs-extra: "npm:^8.1.0"
    graceful-fs: "npm:^4.1.3"
    prompts: "npm:^2.4.2"
    semver: "npm:^7.5.2"
  bin:
    rnc-cli: build/bin.js
  checksum: 10c0/4f2404301e7d12134dfa3f540d89f6a7b0ee9dd2125fe67d8c91a75cb6aa53367fc4db834c840b484cf1781cf5f4370b26ff9289beeba0e143b5febfadfd305d
  languageName: node
  linkType: hard

"@react-native-community/netinfo@npm:11.3.1":
  version: 11.3.1
  resolution: "@react-native-community/netinfo@npm:11.3.1"
  peerDependencies:
    react-native: ">=0.59"
  checksum: 10c0/a74348321b262e6a99d01e5fa71473b34c12319845139cdf8e95fcd474b4022f9a3f50e506acecd7538ca44afbd75df09fce048184b1ea322edf45ed21ab134a
  languageName: node
  linkType: hard

"@react-native/assets-registry@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/assets-registry@npm:0.74.87"
  checksum: 10c0/3a074d492ecc87916ad8a857969e23242f506d331776fc4128aa5be52feb3f035750ddcca6d57adce912fa774538366ed7e3dab9a39416d241f220f5f48d29c8
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/babel-plugin-codegen@npm:0.74.87"
  dependencies:
    "@react-native/codegen": "npm:0.74.87"
  checksum: 10c0/9c299db211c607d6ddfd96577fdda5990909ee833590650f250a6fa01b07c414d240b2637e714a852dab5fba28a4056b42e5e023661eb54743cc4269bbe5e693
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/babel-preset@npm:0.74.87"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/plugin-proposal-async-generator-functions": "npm:^7.0.0"
    "@babel/plugin-proposal-class-properties": "npm:^7.18.0"
    "@babel/plugin-proposal-export-default-from": "npm:^7.0.0"
    "@babel/plugin-proposal-logical-assignment-operators": "npm:^7.18.0"
    "@babel/plugin-proposal-nullish-coalescing-operator": "npm:^7.18.0"
    "@babel/plugin-proposal-numeric-separator": "npm:^7.0.0"
    "@babel/plugin-proposal-object-rest-spread": "npm:^7.20.0"
    "@babel/plugin-proposal-optional-catch-binding": "npm:^7.0.0"
    "@babel/plugin-proposal-optional-chaining": "npm:^7.20.0"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.8.0"
    "@babel/plugin-syntax-export-default-from": "npm:^7.0.0"
    "@babel/plugin-syntax-flow": "npm:^7.18.0"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.0.0"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.0.0"
    "@babel/plugin-transform-arrow-functions": "npm:^7.0.0"
    "@babel/plugin-transform-async-to-generator": "npm:^7.20.0"
    "@babel/plugin-transform-block-scoping": "npm:^7.0.0"
    "@babel/plugin-transform-classes": "npm:^7.0.0"
    "@babel/plugin-transform-computed-properties": "npm:^7.0.0"
    "@babel/plugin-transform-destructuring": "npm:^7.20.0"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.20.0"
    "@babel/plugin-transform-function-name": "npm:^7.0.0"
    "@babel/plugin-transform-literals": "npm:^7.0.0"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.0.0"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.0.0"
    "@babel/plugin-transform-parameters": "npm:^7.0.0"
    "@babel/plugin-transform-private-methods": "npm:^7.22.5"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.22.11"
    "@babel/plugin-transform-react-display-name": "npm:^7.0.0"
    "@babel/plugin-transform-react-jsx": "npm:^7.0.0"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.0.0"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.0.0"
    "@babel/plugin-transform-runtime": "npm:^7.0.0"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.0.0"
    "@babel/plugin-transform-spread": "npm:^7.0.0"
    "@babel/plugin-transform-sticky-regex": "npm:^7.0.0"
    "@babel/plugin-transform-typescript": "npm:^7.5.0"
    "@babel/plugin-transform-unicode-regex": "npm:^7.0.0"
    "@babel/template": "npm:^7.0.0"
    "@react-native/babel-plugin-codegen": "npm:0.74.87"
    babel-plugin-transform-flow-enums: "npm:^0.0.2"
    react-refresh: "npm:^0.14.0"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/b8db68da99891eeea3991e74bde06fa07e5668106c6e0dbee03458a58005111004e426fedb750b888a19310037335caace14ef324245d3a7469808b9f7e822f3
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/codegen@npm:0.74.87"
  dependencies:
    "@babel/parser": "npm:^7.20.0"
    glob: "npm:^7.1.1"
    hermes-parser: "npm:0.19.1"
    invariant: "npm:^2.2.4"
    jscodeshift: "npm:^0.14.0"
    mkdirp: "npm:^0.5.1"
    nullthrows: "npm:^1.1.1"
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  checksum: 10c0/753f55ab78c05e9eb2cae4e8f01e48f173de2dc75b56edd9003dad31e2bde3eaab592dfce95fa43cb2ab0468d9130d107ce854c1ed2eceb23b94122d897ed4ce
  languageName: node
  linkType: hard

"@react-native/community-cli-plugin@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/community-cli-plugin@npm:0.74.87"
  dependencies:
    "@react-native-community/cli-server-api": "npm:13.6.9"
    "@react-native-community/cli-tools": "npm:13.6.9"
    "@react-native/dev-middleware": "npm:0.74.87"
    "@react-native/metro-babel-transformer": "npm:0.74.87"
    chalk: "npm:^4.0.0"
    execa: "npm:^5.1.1"
    metro: "npm:^0.80.3"
    metro-config: "npm:^0.80.3"
    metro-core: "npm:^0.80.3"
    node-fetch: "npm:^2.2.0"
    querystring: "npm:^0.2.1"
    readline: "npm:^1.3.0"
  checksum: 10c0/7db54907075a48707d55028557258120d4600b4131c7163acd147177cced638184371756e47101555b9636f3803c0f9df611a5c0383492a13d17e45addb07236
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.74.85":
  version: 0.74.85
  resolution: "@react-native/debugger-frontend@npm:0.74.85"
  checksum: 10c0/66d0bc2c969aead72259ccdc3b865c55777ff66354004c4fbd52e5db23a41e792ab861c45dc052bf031a4465edecfacd8f73ffbb89f2e7986bd78dac5e30a49e
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/debugger-frontend@npm:0.74.87"
  checksum: 10c0/9b2bf0a2d1598d96af56d57226b5f6448c7ff63a7395226bd4c68f2319f1b4591468bb71e5aeeb2a2508d639d7752bbd036aa3f86a7a71436f9acbe6f1431b36
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.74.85":
  version: 0.74.85
  resolution: "@react-native/dev-middleware@npm:0.74.85"
  dependencies:
    "@isaacs/ttlcache": "npm:^1.4.1"
    "@react-native/debugger-frontend": "npm:0.74.85"
    "@rnx-kit/chromium-edge-launcher": "npm:^1.0.0"
    chrome-launcher: "npm:^0.15.2"
    connect: "npm:^3.6.5"
    debug: "npm:^2.2.0"
    node-fetch: "npm:^2.2.0"
    nullthrows: "npm:^1.1.1"
    open: "npm:^7.0.3"
    selfsigned: "npm:^2.4.1"
    serve-static: "npm:^1.13.1"
    temp-dir: "npm:^2.0.0"
    ws: "npm:^6.2.2"
  checksum: 10c0/e46be4530872eb859e94ff25793a5d4c7264a40e3dfae99b5f751bcb2704201acedaa59b06ed68ef549eaf0f2f4b28e0f21f1b25bc03566f4b5719d0d53f6f73
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/dev-middleware@npm:0.74.87"
  dependencies:
    "@isaacs/ttlcache": "npm:^1.4.1"
    "@react-native/debugger-frontend": "npm:0.74.87"
    "@rnx-kit/chromium-edge-launcher": "npm:^1.0.0"
    chrome-launcher: "npm:^0.15.2"
    connect: "npm:^3.6.5"
    debug: "npm:^2.2.0"
    node-fetch: "npm:^2.2.0"
    nullthrows: "npm:^1.1.1"
    open: "npm:^7.0.3"
    selfsigned: "npm:^2.4.1"
    serve-static: "npm:^1.13.1"
    temp-dir: "npm:^2.0.0"
    ws: "npm:^6.2.2"
  checksum: 10c0/d641799b9e0a8918e6e3b7500d376f11d3a33d736d03ed90b3cf55c6df45788bcf8aae047ca00b1e5f269fdf44d4884676bc0f397e9be3bbcec3a53a2db4d15f
  languageName: node
  linkType: hard

"@react-native/gradle-plugin@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/gradle-plugin@npm:0.74.87"
  checksum: 10c0/99865aab4e7aeafa053b3b2cd4871ebddebd771ae747c0104e740bb592083162a53dba274199e87b5bed4a5b8fe2d99c0eeda4eeaa228304192a38a6406479b3
  languageName: node
  linkType: hard

"@react-native/js-polyfills@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/js-polyfills@npm:0.74.87"
  checksum: 10c0/8cfc367fd31798f13a4a6a379d91c21149632bb6d200873e26a4448cd37a18ee8c6d9ea08a71ba3e7c5f5aedbc0b41454e4d60e920e00727fef52f0820b9642d
  languageName: node
  linkType: hard

"@react-native/metro-babel-transformer@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/metro-babel-transformer@npm:0.74.87"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@react-native/babel-preset": "npm:0.74.87"
    hermes-parser: "npm:0.19.1"
    nullthrows: "npm:^1.1.1"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/d5f62d268e6e18db0fb290017f8f168dcf9b7399df3f3772655b69d8e14752f786ef0bffc5ea079b4abdc16f9913b2163b966e0aa2022f1c5bb506055eda47b5
  languageName: node
  linkType: hard

"@react-native/normalize-color@npm:*":
  version: 2.1.0
  resolution: "@react-native/normalize-color@npm:2.1.0"
  checksum: 10c0/95814a1e2aac9c00dfc2c65f9e2caec07f70d3dba903b5640f5cf24605bf39863e572f2a5138a85d1c514fb3c33f6931595e0a9f738a58b5c220ee74f2bec13b
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:*, @react-native/normalize-colors@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/normalize-colors@npm:0.74.87"
  checksum: 10c0/7ab19e1d8df02d2c724d527993dc4f62f977b7836fa9648134e84d387d0af3a7697a688b19129223f23b972c37dd06ce753d914b5aa1667d25f0e8852eb7c335
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:0.74.85":
  version: 0.74.85
  resolution: "@react-native/normalize-colors@npm:0.74.85"
  checksum: 10c0/44fbb2e78ed4656b78b32aa41b79e2e8b6264e1577b892a6f81205a7991490aad62ae96b3900d6f6e1609ffd5bab7ed1760aa814f119a90c05d13ab80942fda7
  languageName: node
  linkType: hard

"@react-native/virtualized-lists@npm:0.74.87":
  version: 0.74.87
  resolution: "@react-native/virtualized-lists@npm:0.74.87"
  dependencies:
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
  peerDependencies:
    "@types/react": ^18.2.6
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/0082a658efb2369f2c2bf052a8e842fd22b6895e1f25dfaae8220819aee1df4cd466c0b521bdd0cc5f6f1e505c6c1a56f97b998521e49898badf7e06b5da1907
  languageName: node
  linkType: hard

"@react-navigation/bottom-tabs@npm:^6.5.9":
  version: 6.5.11
  resolution: "@react-navigation/bottom-tabs@npm:6.5.11"
  dependencies:
    "@react-navigation/elements": "npm:^1.3.21"
    color: "npm:^4.2.3"
    warn-once: "npm:^0.1.0"
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
    react-native-screens: ">= 3.0.0"
  checksum: 10c0/63a3db2924a65757f39ee129caafabf1584e562b18648f9048e7dadcc00bf4adac4dedf13c7732737abb84d3f0cd7c5b3e8cda1431c67823c5f73793a59aeecc
  languageName: node
  linkType: hard

"@react-navigation/core@npm:^6.4.10":
  version: 6.4.10
  resolution: "@react-navigation/core@npm:6.4.10"
  dependencies:
    "@react-navigation/routers": "npm:^6.1.9"
    escape-string-regexp: "npm:^4.0.0"
    nanoid: "npm:^3.1.23"
    query-string: "npm:^7.1.3"
    react-is: "npm:^16.13.0"
    use-latest-callback: "npm:^0.1.7"
  peerDependencies:
    react: "*"
  checksum: 10c0/1cb33a4196a48858cfa5b8c403160d1133bf3d3c2b2442705b9ca19b3b0583fd60ec1bb6896579c0bf38fda6a315deb41fdd7292645d4d67b6b55577dd9dd85a
  languageName: node
  linkType: hard

"@react-navigation/elements@npm:^1.3.21":
  version: 1.3.21
  resolution: "@react-navigation/elements@npm:1.3.21"
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
  checksum: 10c0/aac75c134345eccc2618f397b755a13556e52fe56db85d20c9c43eec4a9aebab5998a0623c637076ce52124c195c97260b74d157538f34356559715748115ad1
  languageName: node
  linkType: hard

"@react-navigation/native-stack@npm:^6.9.13":
  version: 6.9.16
  resolution: "@react-navigation/native-stack@npm:6.9.16"
  dependencies:
    "@react-navigation/elements": "npm:^1.3.21"
    warn-once: "npm:^0.1.0"
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
    react-native-screens: ">= 3.0.0"
  checksum: 10c0/1686f0dbcf2e3b9bb62d5891c214e0604d22cc8ce007598a7541ebf41d3401fc2b33f78daebca8fe4653d33b3212b455fc88034de09de79145ff6c1b4b499b64
  languageName: node
  linkType: hard

"@react-navigation/native@npm:^6.1.8":
  version: 6.1.9
  resolution: "@react-navigation/native@npm:6.1.9"
  dependencies:
    "@react-navigation/core": "npm:^6.4.10"
    escape-string-regexp: "npm:^4.0.0"
    fast-deep-equal: "npm:^3.1.3"
    nanoid: "npm:^3.1.23"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/09b2656be8cfdbe271b22e293aa26faad8481711127b8aedc3cd3681199a0a30e40413c053d60031fb2739696ccbaacae918b24eccdf16daa723364872900878
  languageName: node
  linkType: hard

"@react-navigation/routers@npm:^6.1.9":
  version: 6.1.9
  resolution: "@react-navigation/routers@npm:6.1.9"
  dependencies:
    nanoid: "npm:^3.1.23"
  checksum: 10c0/5b58014cf29bb71c7dc01201e271d55f0ecfe6d38d064179eeff0fc0b5cb739d4d9906eb133f100d25fc674c72c24aa65d5f6bfc3d036d79f7c5d1936391c605
  languageName: node
  linkType: hard

"@react-stately/checkbox@npm:3.0.3":
  version: 3.0.3
  resolution: "@react-stately/checkbox@npm:3.0.3"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/toggle": "npm:^3.2.3"
    "@react-stately/utils": "npm:^3.2.2"
    "@react-types/checkbox": "npm:^3.2.3"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/36058ea11da0678c308a4c9c547016257fec840f3c907e943cedaabca01a35962115294c0212d74a70ca4007987436981257190b13371bc7837945aa7da0f2a9
  languageName: node
  linkType: hard

"@react-stately/checkbox@npm:^3.5.1":
  version: 3.5.1
  resolution: "@react-stately/checkbox@npm:3.5.1"
  dependencies:
    "@react-stately/toggle": "npm:^3.6.3"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/checkbox": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/30141bcd607fd4b8106956e54270853b4f6fbd734967f046275907a9c3798f624ee21fe5cfb5a075bcc64be65f8266a98e798864dffd01977701840953c01c45
  languageName: node
  linkType: hard

"@react-stately/collections@npm:3.3.0":
  version: 3.3.0
  resolution: "@react-stately/collections@npm:3.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-types/shared": "npm:^3.2.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/b219fb6f67bc22f70e1057b5a02403272bcc1b9266879baea61750646b4ac49d64ec5794a542f06161e408e2e45ac414de203f9776b0e59a5ced8d085c1b9bd1
  languageName: node
  linkType: hard

"@react-stately/collections@npm:^3.10.2":
  version: 3.10.2
  resolution: "@react-stately/collections@npm:3.10.2"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/b901a37cf110801b6c5bcc7de45a26f771cff1057c8b8174fb15e376c5f93ed7285c78aef3baf132aadd44a336e44bf0282c1fbbf00388d5ed81a1be2eef1b2e
  languageName: node
  linkType: hard

"@react-stately/combobox@npm:3.0.0-alpha.1":
  version: 3.0.0-alpha.1
  resolution: "@react-stately/combobox@npm:3.0.0-alpha.1"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/list": "npm:^3.2.2"
    "@react-stately/menu": "npm:^3.1.0"
    "@react-stately/select": "npm:^3.1.0"
    "@react-stately/utils": "npm:^3.2.0"
    "@react-types/combobox": "npm:3.0.0-alpha.1"
    "@react-types/shared": "npm:^3.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/0afb016166a457c71b9b6d05f099daa26b08dda3919e6e462cf09faac3fcb4749a2d8e7b2cc4dfa775aeae3274cce8751575a65e29449ba171b0ba6794c01fd9
  languageName: node
  linkType: hard

"@react-stately/combobox@npm:^3.7.1":
  version: 3.7.1
  resolution: "@react-stately/combobox@npm:3.7.1"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/list": "npm:^3.10.0"
    "@react-stately/menu": "npm:^3.5.6"
    "@react-stately/select": "npm:^3.5.5"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/combobox": "npm:^3.8.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/8c505034f13faf4be8bd4f60f9e96d69f863eeed6e7a296632852484af06111e67fbcd92df6e1b5147a95d4a93f92cf91bc24609632dd273ba3483e0ee25816e
  languageName: node
  linkType: hard

"@react-stately/flags@npm:^3.0.0":
  version: 3.0.0
  resolution: "@react-stately/flags@npm:3.0.0"
  dependencies:
    "@swc/helpers": "npm:^0.4.14"
  checksum: 10c0/107a70bd3ff6008844d34f0be4537db82f2381f4829ae2809471323a2cdeec8aae9e73a02d6d57833ab375ec4b5207dec4dd4e05e04934370f17607e3c0feba8
  languageName: node
  linkType: hard

"@react-stately/grid@npm:^3.8.2":
  version: 3.8.2
  resolution: "@react-stately/grid@npm:3.8.2"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-types/grid": "npm:^3.2.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/402880ebd30860e0342cf8ffb222568a60ec05316a979a055e68f809987db5179bc284f021fde4706163bc9dbf36a25e297abe753ee8aa89b4271b1d37f91f5a
  languageName: node
  linkType: hard

"@react-stately/layout@npm:^3.13.3":
  version: 3.13.3
  resolution: "@react-stately/layout@npm:3.13.3"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/table": "npm:^3.11.2"
    "@react-stately/virtualizer": "npm:^3.6.4"
    "@react-types/grid": "npm:^3.2.2"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/table": "npm:^3.9.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/e884bd207b53128e4aebf940d940e797844d7f41572847e9d721ca103776f09129ae726088899004ca2e735fe03fb48d9a53ab89390caeaa38d12b1031b22a96
  languageName: node
  linkType: hard

"@react-stately/list@npm:^3.10.0, @react-stately/list@npm:^3.2.2":
  version: 3.10.0
  resolution: "@react-stately/list@npm:3.10.0"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/44270d580790d2664c9f898db24877bf275f659e36b05e3abae231007be5dd65e85441cf46523e3199fcf93643b66fea24afde439bdf98e8b0a74c27547b13f6
  languageName: node
  linkType: hard

"@react-stately/menu@npm:^3.1.0, @react-stately/menu@npm:^3.5.6":
  version: 3.5.6
  resolution: "@react-stately/menu@npm:3.5.6"
  dependencies:
    "@react-stately/overlays": "npm:^3.6.3"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/menu": "npm:^3.9.5"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/1a08321daf538b0e855d20526c814533b9089112a6c94413bced0aa417560776377a4c31155a13422e1ba00680302b0b3d9a6627b95c1fe1f371637be003f36d
  languageName: node
  linkType: hard

"@react-stately/overlays@npm:^3.1.1, @react-stately/overlays@npm:^3.6.3":
  version: 3.6.3
  resolution: "@react-stately/overlays@npm:3.6.3"
  dependencies:
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/overlays": "npm:^3.8.3"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/e237a616ad4b744bdd9c4fcddb998f1fcd1e23ee1cb26af6140671e2f3a394b8fa60a2f172a14bce11bac28adefcc3a3a311ce342cfc4a909a4ffee1f4bb5657
  languageName: node
  linkType: hard

"@react-stately/radio@npm:3.2.1":
  version: 3.2.1
  resolution: "@react-stately/radio@npm:3.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/utils": "npm:^3.1.1"
    "@react-types/radio": "npm:^3.1.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/3d1bbd8492fe94843344d6922449e9accfdb6823b7bbd6bb878016542c44fc30c747ae17ab02c61ca0396dfb56ada943fed0bb1773666d7583c14fe90f4ff95d
  languageName: node
  linkType: hard

"@react-stately/radio@npm:^3.2.1, @react-stately/radio@npm:^3.9.1":
  version: 3.9.1
  resolution: "@react-stately/radio@npm:3.9.1"
  dependencies:
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/radio": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/a6c29f055a09f94fd086218e8a6ec7555ab2ab24e336327ee059f5d1220b1485d1a4f0f98209e2211a12f29bab1a51a29c7db4729a0561db16e57322516bbf5b
  languageName: node
  linkType: hard

"@react-stately/select@npm:^3.1.0, @react-stately/select@npm:^3.5.5":
  version: 3.5.5
  resolution: "@react-stately/select@npm:3.5.5"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/list": "npm:^3.10.0"
    "@react-stately/menu": "npm:^3.5.6"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/select": "npm:^3.8.4"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/317570e5d3a6f0b37573ccc952d56284286442527000e99d841b38e14265819baae2a6b26d67ad0eafe83f3fade1380921d3a1d14b0095079066fc776c92d68a
  languageName: node
  linkType: hard

"@react-stately/selection@npm:^3.14.0":
  version: 3.14.0
  resolution: "@react-stately/selection@npm:3.14.0"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/384f08fec3d0a0acbcfaabaedbccc9fd4500170d65e275ff34a882a1cf32f2fa9643db73cad9ab7d37a382e12b8b0e506fdf7b7340aae15d8ac450e41449af67
  languageName: node
  linkType: hard

"@react-stately/slider@npm:3.0.1":
  version: 3.0.1
  resolution: "@react-stately/slider@npm:3.0.1"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-aria/i18n": "npm:^3.3.0"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-stately/utils": "npm:^3.2.0"
    "@react-types/slider": "npm:^3.0.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/fa5cb62dedaaf1311129abb0b7a23c03484d95d4f13543d38153a408198dd9a85d623d1d56e1fd8fd6a71ffe6b853ba5519dc01f01aa1a3e4aab69b9fceefaf0
  languageName: node
  linkType: hard

"@react-stately/slider@npm:^3.0.1, @react-stately/slider@npm:^3.4.4":
  version: 3.4.4
  resolution: "@react-stately/slider@npm:3.4.4"
  dependencies:
    "@react-aria/i18n": "npm:^3.8.4"
    "@react-aria/utils": "npm:^3.21.1"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/slider": "npm:^3.6.2"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/85297190aad15c66fffc7d30f01d655b5ac8b8b46ae54c5bf6da19cbec6017152e6f12e2963d7bdf26d86d10e265c1a82b6ed33beb578d927457cd4f33c5d5c5
  languageName: node
  linkType: hard

"@react-stately/table@npm:^3.11.2":
  version: 3.11.2
  resolution: "@react-stately/table@npm:3.11.2"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/flags": "npm:^3.0.0"
    "@react-stately/grid": "npm:^3.8.2"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/grid": "npm:^3.2.2"
    "@react-types/shared": "npm:^3.21.0"
    "@react-types/table": "npm:^3.9.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/76af35e2880e6524e75b9e9593bddbb92af45c606352edd37a7755f8b05805aa2bc79fec0ca1c91a0756ef25268734c4ee460a6814756b39ce1b4d5d539eeedd
  languageName: node
  linkType: hard

"@react-stately/tabs@npm:3.0.0-alpha.0":
  version: 3.0.0-alpha.0
  resolution: "@react-stately/tabs@npm:3.0.0-alpha.0"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/list": "npm:^3.2.2"
    "@react-stately/utils": "npm:^3.0.0-alpha.1"
    "@react-types/tabs": "npm:3.0.0-alpha.2"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/af839bd56e926051602e9069a93178e6c68505e9d466a5c4cad8f55c08d65a3e5ba1286fb0e3c8a608bb79ed4f13e5c43dcdc94fa6b52a48c9aaee56a13bbc32
  languageName: node
  linkType: hard

"@react-stately/tabs@npm:3.0.0-alpha.1":
  version: 3.0.0-alpha.1
  resolution: "@react-stately/tabs@npm:3.0.0-alpha.1"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/list": "npm:^3.2.2"
    "@react-stately/utils": "npm:^3.2.0"
    "@react-types/tabs": "npm:3.0.0-alpha.2"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/9ff2ba4045ad008de31320bddd7477bd925e7891c9b8859c8e7c6e55c10314e034c47b7f48dad39771e7a27f499bd1717556c94fb70be66458c283152cd4951d
  languageName: node
  linkType: hard

"@react-stately/toggle@npm:3.2.1":
  version: 3.2.1
  resolution: "@react-stately/toggle@npm:3.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    "@react-stately/utils": "npm:^3.1.1"
    "@react-types/checkbox": "npm:^3.2.1"
    "@react-types/shared": "npm:^3.2.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/638677106e185ac418709121251e3b022ec14e8627854d0b47ec16976550ec53b285f86485bd8e4faab974b9f7e64f9c51a277bf0643856495fdce5ab2a448db
  languageName: node
  linkType: hard

"@react-stately/toggle@npm:^3.2.1, @react-stately/toggle@npm:^3.2.3, @react-stately/toggle@npm:^3.6.3":
  version: 3.6.3
  resolution: "@react-stately/toggle@npm:3.6.3"
  dependencies:
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/checkbox": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/75b22562b27e6384db068cc2846e321f592529e2ace7d478f7cd04816620f1d612bfd13fbb992e4a711c3da43621605520b8b8d50d14be854658d73ff5420e59
  languageName: node
  linkType: hard

"@react-stately/tree@npm:^3.7.3":
  version: 3.7.3
  resolution: "@react-stately/tree@npm:3.7.3"
  dependencies:
    "@react-stately/collections": "npm:^3.10.2"
    "@react-stately/selection": "npm:^3.14.0"
    "@react-stately/utils": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/764f159b40691a4b84616803312b11e85f09d695e3bb91c1886ef4c54b8d1633549f7a18db4453bd50cd37bd137e99b47284a26ed8a04c65066b55d6abb3bade
  languageName: node
  linkType: hard

"@react-stately/utils@npm:^3.0.0-alpha.1, @react-stately/utils@npm:^3.1.1, @react-stately/utils@npm:^3.2.0, @react-stately/utils@npm:^3.2.2, @react-stately/utils@npm:^3.8.0":
  version: 3.8.0
  resolution: "@react-stately/utils@npm:3.8.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/dea3918e09af26d67fe0355220c8632c537918bafde69442ed1f89cb3ac70fe1bf9c9c839f744c99af2db0a203ec452c4d9e5e950c0265ecb0de7422f95d1d0e
  languageName: node
  linkType: hard

"@react-stately/virtualizer@npm:^3.6.4":
  version: 3.6.4
  resolution: "@react-stately/virtualizer@npm:3.6.4"
  dependencies:
    "@react-aria/utils": "npm:^3.21.1"
    "@react-types/shared": "npm:^3.21.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/88879d6a6da0cbab0e5a1d92816aabacae1bda5bd4beac76f93386da76c0ff5387f6f05a50f4581261a5afb66019baa50da99ebc019491372decf284d58c7f10
  languageName: node
  linkType: hard

"@react-types/button@npm:^3.3.1, @react-types/button@npm:^3.9.0":
  version: 3.9.0
  resolution: "@react-types/button@npm:3.9.0"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/2d7136df199cb9bb5dba25888b6acc0cc22df60a20f53032f08c0e41d5c0b46ea2bd5fb39c8d146cd8894ab41e97cf1ac9fa77f21353b7c5042189a7cd70468b
  languageName: node
  linkType: hard

"@react-types/checkbox@npm:^3.2.1, @react-types/checkbox@npm:^3.2.3, @react-types/checkbox@npm:^3.5.2":
  version: 3.5.2
  resolution: "@react-types/checkbox@npm:3.5.2"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/2d80956dc04041455b69efc181b906971c8963cf27d8be55b407b9d85abe036f14161310f7c070b77d2662bf414af243aeb5201850524ff65df6e6d5d26b3eda
  languageName: node
  linkType: hard

"@react-types/combobox@npm:3.0.0-alpha.1":
  version: 3.0.0-alpha.1
  resolution: "@react-types/combobox@npm:3.0.0-alpha.1"
  dependencies:
    "@react-types/shared": "npm:^3.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/953a8361c5e4f2103012293decb4d5083aba021795ba874ba1f991d3cba40a597c6390fb36134c93177373cd5557fffc413d5aa6b75c6d9989baa6b364c0f05d
  languageName: node
  linkType: hard

"@react-types/combobox@npm:^3.8.1":
  version: 3.8.1
  resolution: "@react-types/combobox@npm:3.8.1"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/ccb3fae2357540c28c14a98c7835d1983c40b228401fb3bc81dea1c4742eb9038bb1a9bbd4b85abdca6d31585c39b249cec1a4c5e78fc2688907f723e63d9fa8
  languageName: node
  linkType: hard

"@react-types/grid@npm:^3.2.2":
  version: 3.2.2
  resolution: "@react-types/grid@npm:3.2.2"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/47065a73bcbd9fda3dc69506faa83164552b05c2d74ff530af10acc1321d9e60477d2c55d8569874abf9fa76eb890586fb053e361665cb9e101fc2884e6f4f67
  languageName: node
  linkType: hard

"@react-types/label@npm:^3.8.1":
  version: 3.8.1
  resolution: "@react-types/label@npm:3.8.1"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/6118f0a50d0b9f0434c7617b9179daf07a30cee34a82656563fcfff79d1454f3355fc880e32c72591fcd16d540c3185d9cb6a521e46c188db6a8d9080cd6d26f
  languageName: node
  linkType: hard

"@react-types/listbox@npm:^3.1.1, @react-types/listbox@npm:^3.4.5":
  version: 3.4.5
  resolution: "@react-types/listbox@npm:3.4.5"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/87e9a264fc63cb88fa8e49b7a6b42399687045a577f364a168224ddbf11b0eafc73bf7fb4988ffc48fa8c976e582cdc12bd3c02b96b49461351e9ba7d8ccbef2
  languageName: node
  linkType: hard

"@react-types/menu@npm:^3.9.5":
  version: 3.9.5
  resolution: "@react-types/menu@npm:3.9.5"
  dependencies:
    "@react-types/overlays": "npm:^3.8.3"
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/5061090cdb5ccd338ad93854a37146c0a1734e30b4aff9b84ce6b7c553d4c08da7103609d3e94c88a3927f3d56f3515a0d0a4a8f64c557cc1e2e7322de062e04
  languageName: node
  linkType: hard

"@react-types/overlays@npm:^3.4.0, @react-types/overlays@npm:^3.8.3":
  version: 3.8.3
  resolution: "@react-types/overlays@npm:3.8.3"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/3709783a326680079fa372b4ebd55b0a5c33f7a8d4b395b85e24920ba819ea058fede6c58030f2f72f54cc4980161a054f80a6f40158791d26d4decd12eb67ef
  languageName: node
  linkType: hard

"@react-types/radio@npm:^3.1.1, @react-types/radio@npm:^3.5.2":
  version: 3.5.2
  resolution: "@react-types/radio@npm:3.5.2"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/e847064ca7c3625a28a74b0cdb82d214262eaffbb33a5c68cbe9d452cb12ad6c89bb6300ed2f034c9fed9314d819880d079b84b09feac8e199ea98b8c5ede0cb
  languageName: node
  linkType: hard

"@react-types/select@npm:^3.8.4":
  version: 3.8.4
  resolution: "@react-types/select@npm:3.8.4"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/e640b99a749865dec071479b34dd667350cbb005f9a1de04f377b99d56ad5581803bce5230710bdf05e2ba37660dcb149b6aba17a539e4b5bbe38610938ee4bf
  languageName: node
  linkType: hard

"@react-types/shared@npm:^3.2.1, @react-types/shared@npm:^3.21.0, @react-types/shared@npm:^3.4.0":
  version: 3.21.0
  resolution: "@react-types/shared@npm:3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/a89907dcb82b63d81c56d02df2c9d0dec1754952571922f50861c510666257ff00c7d7c715237fb26c3b4a6ae45b0567243dfa8b3d30b666661f3cdc8a29cffc
  languageName: node
  linkType: hard

"@react-types/slider@npm:^3.0.1, @react-types/slider@npm:^3.6.2":
  version: 3.6.2
  resolution: "@react-types/slider@npm:3.6.2"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/246a5480eeba97b0e54df21f7dfbe06ac93058035ea5441dff76e801434d0a157c4248f2f7594a93025760169a880d0002420b15b61ae02a4f4652fc2cb77cc0
  languageName: node
  linkType: hard

"@react-types/switch@npm:^3.4.2":
  version: 3.4.2
  resolution: "@react-types/switch@npm:3.4.2"
  dependencies:
    "@react-types/checkbox": "npm:^3.5.2"
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/02aa6afb84844c3cfdc211bacbaa84fb297875282e0cf210386f1fafc82c420f600ca44556a9d5711a826026aa9e7b1c22d8c7b9f32f430a545daccebc97bc7a
  languageName: node
  linkType: hard

"@react-types/table@npm:^3.9.0":
  version: 3.9.0
  resolution: "@react-types/table@npm:3.9.0"
  dependencies:
    "@react-types/grid": "npm:^3.2.2"
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/47dfa0f9aee67dc94f3d09a8a56285be9ba9dd6d59a74f722652089736eb641ed650c6b6decbec259d4b1b143ecd841197ef3c42d4b5ac74e2107d820ba3ca8a
  languageName: node
  linkType: hard

"@react-types/tabs@npm:3.0.0-alpha.2":
  version: 3.0.0-alpha.2
  resolution: "@react-types/tabs@npm:3.0.0-alpha.2"
  dependencies:
    "@react-types/shared": "npm:^3.2.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1
  checksum: 10c0/14211a80767939c0ccda3f26910fc892c3a87f649ec20bd03659092e7ad88fb439a5162519ccb733fd309f1fdf6381e1d62b0d2d3d1cd01edd798759c42b0df2
  languageName: node
  linkType: hard

"@react-types/textfield@npm:^3.8.1":
  version: 3.8.1
  resolution: "@react-types/textfield@npm:3.8.1"
  dependencies:
    "@react-types/shared": "npm:^3.21.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
  checksum: 10c0/b5779f34d7e8474067b54cce51f6b1c021db005a518ff340e6cd3c073f60f8c54ffadf92d14ed3f8510862369ae034a54819b1be2469cfbfadac3ef559739c34
  languageName: node
  linkType: hard

"@rnx-kit/chromium-edge-launcher@npm:^1.0.0":
  version: 1.0.0
  resolution: "@rnx-kit/chromium-edge-launcher@npm:1.0.0"
  dependencies:
    "@types/node": "npm:^18.0.0"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
    mkdirp: "npm:^1.0.4"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/21182379a914ad244b556e794eb6bc6dc63a099cbd2f3eb315a13bd431dc6f24ca096ffb465ad76465144d02969f538a93ef7ef1b2280135174fdae4db5206b3
  languageName: node
  linkType: hard

"@segment/loosely-validate-event@npm:^2.0.0":
  version: 2.0.0
  resolution: "@segment/loosely-validate-event@npm:2.0.0"
  dependencies:
    component-type: "npm:^1.2.1"
    join-component: "npm:^1.1.0"
  checksum: 10c0/c083c70c5f0a42a2bc5b685f82830b968d01b5b8de2a9a1c362a3952c6bb33ffbdfcf8196c8ce110a5050f78ff9dcf395832eb55687843c80dc77dfe659b0803
  languageName: node
  linkType: hard

"@shopify/flash-list@npm:1.6.4":
  version: 1.6.4
  resolution: "@shopify/flash-list@npm:1.6.4"
  dependencies:
    recyclerlistview: "npm:4.2.0"
    tslib: "npm:2.4.0"
  peerDependencies:
    "@babel/runtime": "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/4b99f29f8ec2c9405c0f215d207cb489cea383c4c1d93bb594e7bb494204829b6d1b9fd9bad5c3ded5de1a0da098349b7c246cc558fb544834d8224b25f2f4c5
  languageName: node
  linkType: hard

"@sideway/address@npm:^4.1.5":
  version: 4.1.5
  resolution: "@sideway/address@npm:4.1.5"
  dependencies:
    "@hapi/hoek": "npm:^9.0.0"
  checksum: 10c0/638eb6f7e7dba209053dd6c8da74d7cc995e2b791b97644d0303a7dd3119263bcb7225a4f6804d4db2bc4f96e5a9d262975a014f58eae4d1753c27cbc96ef959
  languageName: node
  linkType: hard

"@sideway/formula@npm:^3.0.1":
  version: 3.0.1
  resolution: "@sideway/formula@npm:3.0.1"
  checksum: 10c0/3fe81fa9662efc076bf41612b060eb9b02e846ea4bea5bd114f1662b7f1541e9dedcf98aff0d24400bcb92f113964a50e0290b86e284edbdf6346fa9b7e2bf2c
  languageName: node
  linkType: hard

"@sideway/pinpoint@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sideway/pinpoint@npm:2.0.0"
  checksum: 10c0/d2ca75dacaf69b8fc0bb8916a204e01def3105ee44d8be16c355e5f58189eb94039e15ce831f3d544f229889ccfa35562a0ce2516179f3a7ee1bbe0b71e55b36
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10c0/1227a7b5bd6c6f9584274db996d7f8cee2c8c350534b9d0141fc662eaf1f292ea0ae3ed19e5e5271c8fd390d27e492ca2803acd31a1978be2cdc6be0da711403
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10c0/2e2fb6cc57f227912814085b7b01fede050cd4746ea8d49a1e44d5a0e56a804663b0340ae2f11af7559ea9bf4d087a11f2f646197a660ea3cb04e19efc04aa63
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.4.14":
  version: 0.4.36
  resolution: "@swc/helpers@npm:0.4.36"
  dependencies:
    legacy-swc-helpers: "npm:@swc/helpers@=0.4.14"
    tslib: "npm:^2.4.0"
  checksum: 10c0/b3b12d0305d49474546952db398435d89ce305b8ffe9c73c90c18ba43ccb16cc90b043c199c813190826316835d590b87c2c3779aacc55382c6e959eca458d2d
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.0":
  version: 0.5.3
  resolution: "@swc/helpers@npm:0.5.3"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/7003b9dafad38e27a4474a3dcf78d2aee85ce4bb7ee171ecc2d5b533768fb4f8fc9dbd6a1c3a9df5f8001e61ff416b93681e0a7dcbdc0df109b3e09c50cd4615
  languageName: node
  linkType: hard

"@types/hammerjs@npm:^2.0.36":
  version: 2.0.43
  resolution: "@types/hammerjs@npm:2.0.43"
  checksum: 10c0/6feaef2946b4f42216d97d199e9ca939e038e35c598df6755a9d98372cc2c6a16804bf8acd19273575b165cba185c9c136a4f5463f41fb4d7395fe253cb1660c
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10c0/3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^1.1.1":
  version: 1.1.2
  resolution: "@types/istanbul-reports@npm:1.1.2"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/80b76715f4ac74a4ddfc82d7942b2faaefbe9fdce8e7dfdfa497b3fb60a3e707b632c6e70e1565cfe30045eaebaf7aad0d6c3d102652d1da8fdb0bf095924eb3
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/node-forge@npm:^1.3.0":
  version: 1.3.13
  resolution: "@types/node-forge@npm:1.3.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/95d004c2af465f27c3eefc805d4de5a4a9bfb8f715b3e733ca085f7cd4e33b23cde1928ad7a14691957c40d86ca7c7678790d34ebf3223aecf8bda38327cb714
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.4.9
  resolution: "@types/node@npm:20.4.9"
  checksum: 10c0/e7b84b1d204bf77674bb1a500861b296128694a34cd6040e2e87ac7fb7e81d28e94c2482b44a92b378fe4f095661ab0114a4261dbc7e11e3699a600aba8d8cc6
  languageName: node
  linkType: hard

"@types/node@npm:^18.0.0":
  version: 18.19.119
  resolution: "@types/node@npm:18.19.119"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/da8a015833e4e2d2da3dbc1129b0ce6681d7b5b6a0eb8884e618cc7a28bc2cdf24724c481536f1ddf5fb769208604b7d5502f566106f26068dc8abe72375fd4f
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10c0/1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/stylis@npm:^4.0.2":
  version: 4.2.2
  resolution: "@types/stylis@npm:4.2.2"
  checksum: 10c0/1ccc76a1f88bebbca946daafe4a999761240cf570b8b79f26f264fb5ba0737194ba938378d094a5dd769b5f905a39967bcde4b726d061f467313b2c2dd6d9815
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10c0/e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^13.0.0":
  version: 13.0.12
  resolution: "@types/yargs@npm:13.0.12"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/81fdac6832d69f2f2a33bb3d77887f571677d5a9ccfd5a171ff3e76252a6c6a9773850a0df6ba9ed0328433a36596488ec4e2ce5d9bc49d713a59bbfef8e12a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^15.0.0":
  version: 15.0.19
  resolution: "@types/yargs@npm:15.0.19"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/9fe9b8645304a628006cbba2d1990fb015e2727274d0e3853f321a379a1242d1da2c15d2f56cff0d4313ae94f0383ccf834c3bded9fb3589608aefb3432fcf00
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@urql/core@npm:2.3.6, @urql/core@npm:>=2.3.1":
  version: 2.3.6
  resolution: "@urql/core@npm:2.3.6"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.0"
    wonka: "npm:^4.0.14"
  peerDependencies:
    graphql: ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10c0/101ac57a8bd4f6b9747262ed546d236d22aa620585d979832b3d30dccf6a11400e463e72b836d850a7a603404842fca6c39107257f0c456f38605391da8cdab3
  languageName: node
  linkType: hard

"@urql/exchange-retry@npm:0.3.0":
  version: 0.3.0
  resolution: "@urql/exchange-retry@npm:0.3.0"
  dependencies:
    "@urql/core": "npm:>=2.3.1"
    wonka: "npm:^4.0.14"
  peerDependencies:
    graphql: ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0
  checksum: 10c0/6ea0ecbc56de94f228627d06cd084b5d71d605884a68b3f7f03873ac538c290f9500e67938635650edd7f32e53dfa9c4b6e38f5aa8fd48f061b6135c42f3a204
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:~0.7.7":
  version: 0.7.13
  resolution: "@xmldom/xmldom@npm:0.7.13"
  checksum: 10c0/cb02e4e8d986acf18578a5f25d1bce5e18d08718f40d8a0cdd922a4c112c8e00daf94de4e43f9556ed147c696b135f2ab81fa9a2a8a0416f60af15d156b60e40
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:^1.3.7, accepts@npm:^1.3.8, accepts@npm:~1.3.7":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: 10c0/c2c9ab7599692d594b6a161559ada307b7a624fa4c7b03e3afdb5a5e31cd0e53269115b620fcab024c5ac6a6f37fa5eb2e004f076ad30f5f7e6b8b671f7b35fe
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10c0/a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ajv@npm:^8.11.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/ac4f72adf727ee425e049bc9d8b31d4a57e1c90da8d28bcd23d60781b12fcd6fc3d68db5df16994c57b78b94eed7988f5a6b482fd376dc5b084125e20a0a622e
  languageName: node
  linkType: hard

"anser@npm:^1.4.9":
  version: 1.4.10
  resolution: "anser@npm:1.4.10"
  checksum: 10c0/ab251c96f6b9b8858e346137b75968ef3d287e10f358cd3981666949093e587defb5f7059a05a929eb44e1b3775bae346a55ab952e74049355e70f81b8b1ef53
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-fragments@npm:^0.2.1":
  version: 0.2.1
  resolution: "ansi-fragments@npm:0.2.1"
  dependencies:
    colorette: "npm:^1.0.7"
    slice-ansi: "npm:^2.0.0"
    strip-ansi: "npm:^5.0.0"
  checksum: 10c0/44e97e558ca2f0b2ca895bfd6ebebeb2e77d674d2e4198ac2d3a05b690193fa35fd185db6e16b92dd0ee854299ea8b4387a99e4155ea62bc8ad4c42154542fd4
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.0.0, ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: 10c0/d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.0, ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 10c0/60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"appdirsjs@npm:^1.2.4":
  version: 1.2.7
  resolution: "appdirsjs@npm:1.2.7"
  checksum: 10c0/79dd8d7a764cdde2b47efc4383e054814be917ba0cd661ee324bdf3fd11542834548316faea31344f96a7ebc898b5f89c11d1418f825a1d40c396bf1ecb0902b
  languageName: node
  linkType: hard

"arg@npm:5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 10c0/ccaf86f4e05d342af6666c569f844bec426595c567d32a8289715087825c2ca7edd8a3d204e4d2fb2aa4602e09a57d0c13ea8c9eea75aac3dbb4af5514e6800e
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"asap@npm:~2.0.3, asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"ast-types@npm:0.15.2":
  version: 0.15.2
  resolution: "ast-types@npm:0.15.2"
  dependencies:
    tslib: "npm:^2.0.1"
  checksum: 10c0/5b26e3656e9e8d1db8c8d14971d0cb88ca0138aacce72171cb4cd4555fc8dc53c07e821c568e57fe147366931708fefd25cb9d7e880d42ce9cb569947844c962
  languageName: node
  linkType: hard

"astral-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "astral-regex@npm:1.0.0"
  checksum: 10c0/ca460207a19d84c65671e1a85940101522d42f31a450cdb8f93b3464e6daeaf4b58a362826a6c11c57e6cd1976403d197abb0447cfc2087993a29b35c6d63b63
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 10c0/0693d378cfe86842a70d4c849595a0bb50dc44c11649640ca982fa90cbfc74e3cc4753b5a0847e51933f2e9c65ce8e05576e75e5e1fd963a086e673735b35969
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axios@npm:^1.4.0":
  version: 1.6.0
  resolution: "axios@npm:1.6.0"
  dependencies:
    follow-redirects: "npm:^1.15.0"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/2879e17b96cbca7e2096d231a44e2d0f03e657d79f8928ea38ec5fbaf5a5b7bf952d580cdb58a66ba328c26eb3528b89d5a32da57cc5cf89813786c044f7e9d6
  languageName: node
  linkType: hard

"babel-core@npm:^7.0.0-bridge.0":
  version: 7.0.0-bridge.0
  resolution: "babel-core@npm:7.0.0-bridge.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f57576e30267be4607d163b7288031d332cf9200ea35efe9fb33c97f834e304376774c28c1f9d6928d6733fcde7041e4010f1248a0519e7730c590d4b07b9608
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.14":
  version: 0.4.14
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.14"
  dependencies:
    "@babel/compat-data": "npm:^7.27.7"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/d74cba0600a6508e86d220bde7164eb528755d91be58020e5ea92ea7fbb12c9d8d2c29246525485adfe7f68ae02618ec428f9a589cac6cbedf53cc3972ad7fbe
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.13.0":
  version: 0.13.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.13.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
    core-js-compat: "npm:^3.43.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/5d8e228da425edc040d8c868486fd01ba10b0440f841156a30d9f8986f330f723e2ee61553c180929519563ef5b64acce2caac36a5a847f095d708dda5d8206d
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.5":
  version: 0.6.5
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.5"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/63aa8ed716df6a9277c6ab42b887858fa9f57a70cc1d0ae2b91bdf081e45d4502848cba306fb60b02f59f99b32fd02ff4753b373cac48ccdac9b7d19dd56f06d
  languageName: node
  linkType: hard

"babel-plugin-react-compiler@npm:0.0.0-experimental-592953e-20240517":
  version: 0.0.0-experimental-592953e-20240517
  resolution: "babel-plugin-react-compiler@npm:0.0.0-experimental-592953e-20240517"
  dependencies:
    "@babel/generator": "npm:7.2.0"
    "@babel/types": "npm:^7.19.0"
    chalk: "npm:4"
    invariant: "npm:^2.2.4"
    pretty-format: "npm:^24"
    zod: "npm:^3.22.4"
    zod-validation-error: "npm:^2.1.0"
  checksum: 10c0/28a48cd55a3157eef5998ede92a68c3f5b081175396f1be8327f1e31eab57c1cee824897bd193db7af09bc45d7c3dcf57514305ad3f236df1bd922ade180fa2e
  languageName: node
  linkType: hard

"babel-plugin-react-native-web@npm:~0.19.10":
  version: 0.19.13
  resolution: "babel-plugin-react-native-web@npm:0.19.13"
  checksum: 10c0/0710db342063182163d58febfb01ef510c9460f0500f9faaf47603d06dda37554f216e6123a099a343eb2067c2dfb43c9d4ca573a9d659662ca429048db11af4
  languageName: node
  linkType: hard

"babel-plugin-transform-flow-enums@npm:^0.0.2":
  version: 0.0.2
  resolution: "babel-plugin-transform-flow-enums@npm:0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow": "npm:^7.12.1"
  checksum: 10c0/aa9d022d8d4be0e7c4f1ff7e5308fe7e0ff4d6f9099449913e3a11c1e81916623a8f36432da180a9aa3f53ea534dca4401fe33d6528f043f40357cfa790ee778
  languageName: node
  linkType: hard

"babel-preset-expo@npm:~11.0.15":
  version: 11.0.15
  resolution: "babel-preset-expo@npm:11.0.15"
  dependencies:
    "@babel/plugin-proposal-decorators": "npm:^7.12.9"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.22.11"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.12.13"
    "@babel/plugin-transform-parameters": "npm:^7.22.15"
    "@babel/preset-react": "npm:^7.22.15"
    "@babel/preset-typescript": "npm:^7.23.0"
    "@react-native/babel-preset": "npm:0.74.87"
    babel-plugin-react-compiler: "npm:0.0.0-experimental-592953e-20240517"
    babel-plugin-react-native-web: "npm:~0.19.10"
    react-refresh: "npm:^0.14.2"
  checksum: 10c0/2e1c31e51031f83d705d3c56a7b19a51bfe8aa39408e7433e3032d74626475f842fdbe011521577878813d9a1bf50ebba128cd6dbe5c10f7feadcd030ae36abb
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base-64@npm:^1.0.0":
  version: 1.0.0
  resolution: "base-64@npm:1.0.0"
  checksum: 10c0/d886cb3236cee0bed9f7075675748b59b32fad623ddb8ce1793c790306aa0f76a03238cad4b3fb398abda6527ce08a5588388533a4ccade0b97e82b9da660e28
  languageName: node
  linkType: hard

"base64-js@npm:^1.2.3, base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"better-opn@npm:~3.0.2":
  version: 3.0.2
  resolution: "better-opn@npm:3.0.2"
  dependencies:
    open: "npm:^8.0.4"
  checksum: 10c0/911ef25d44da75aabfd2444ce7a4294a8000ebcac73068c04a60298b0f7c7506b60421aa4cd02ac82502fb42baaff7e4892234b51e6923eded44c5a11185f2f5
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.51
  resolution: "big-integer@npm:1.6.51"
  checksum: 10c0/c8139662d57f8833a44802f4b65be911679c569535ea73c5cfd3c1c8994eaead1b84b6f63e1db63833e4d4cacb6b6a9e5522178113dfdc8e4c81ed8436f1e8cc
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/02847e1d2cb089c9dc6958add42e3cdeaf07d13f575973963335ac0fdece563a50ac770ac4c8fa06492d2dd276f6cc3b7f08c7cd9c7a7ad0f8d388b2a28def5f
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"bplist-creator@npm:0.0.7":
  version: 0.0.7
  resolution: "bplist-creator@npm:0.0.7"
  dependencies:
    stream-buffers: "npm:~2.2.0"
  checksum: 10c0/37044d0070548da6b7c2eeb9c42a5a2b22b3d7eaf4b49e5b4c3ff0cd9f579902b69eb298bda9af2cbe172bc279caf8e4a889771e9e1ee9f412c1ce5afa16d4a9
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.0":
  version: 0.1.0
  resolution: "bplist-creator@npm:0.1.0"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/86f5fe95f34abd369b381abf0f726e220ecebd60a3d932568ae94895ccf1989a87553e4aee9ab3cfb4f35e6f72319f52aa73028165eec82819ed39f15189d493
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.1":
  version: 0.3.1
  resolution: "bplist-parser@npm:0.3.1"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/00940a60214e8f58246264d389db8817b7f7f968cd544ec4a5843e33f810c7a07294a92060fc507104a1a2921212c053fe8e941fb2129b9b4da5fbb12a08e95c
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.3.1":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/4dc307c11d2511a01255e87e370d4ab6f1962b35fdc27605fd4ce9a557a259c2dc9f87822617ddb1f7aa062a71e30ef20d6103329ac7ce235628f637fb0ed763
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: "npm:^7.0.1"
  checksum: 10c0/321b4d675791479293264019156ca322163f02dc06e3c4cab33bb15cd43d80b51efef69b0930cfde3acd63d126ebca24cd0544fa6f261e093a0fb41ab9dda381
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.25.1":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: "npm:^1.0.30001726"
    electron-to-chromium: "npm:^1.5.173"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/acba5f0bdbd5e72dafae1e6ec79235b7bad305ed104e082ed07c34c38c7cb8ea1bc0f6be1496958c40482e40166084458fc3aee15111f15faa79212ad9081b2a
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10c0/24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"buffer-alloc-unsafe@npm:^1.1.0":
  version: 1.1.0
  resolution: "buffer-alloc-unsafe@npm:1.1.0"
  checksum: 10c0/06b9298c9369621a830227c3797ceb3ff5535e323946d7b39a7398fed8b3243798259b3c85e287608c5aad35ccc551cec1a0a5190cc8f39652e8eee25697fc9c
  languageName: node
  linkType: hard

"buffer-alloc@npm:^1.1.0":
  version: 1.2.0
  resolution: "buffer-alloc@npm:1.2.0"
  dependencies:
    buffer-alloc-unsafe: "npm:^1.1.0"
    buffer-fill: "npm:^1.0.0"
  checksum: 10c0/09d87dd53996342ccfbeb2871257d8cdb25ce9ee2259adc95c6490200cd6e528c5fbae8f30bcc323fe8d8efb0fe541e4ac3bbe9ee3f81c6b7c4b27434cc02ab4
  languageName: node
  linkType: hard

"buffer-fill@npm:^1.0.0":
  version: 1.0.0
  resolution: "buffer-fill@npm:1.0.0"
  checksum: 10c0/55b5654fbbf2d7ceb4991bb537f5e5b5b5b9debca583fee416a74fcec47c16d9e7a90c15acd27577da7bd750b7fa6396e77e7c221e7af138b6d26242381c6e4d
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.4.3, buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"builtins@npm:^1.0.3":
  version: 1.0.3
  resolution: "builtins@npm:1.0.3"
  checksum: 10c0/493afcc1db0a56d174cc85bebe5ca69144f6fdd0007d6cbe6b2434185314c79d83cb867e492b56aa5cf421b4b8a8135bf96ba4c3ce71994cf3da154d1ea59747
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^18.0.2":
  version: 18.0.4
  resolution: "cacache@npm:18.0.4"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: 10c0/6c055bafed9de4f3dcc64ac3dc7dd24e863210902b7c470eb9ce55a806309b3efff78033e3d8b4f7dcc5d467f2db43c6a2857aaaf26f0094b8a351d44c42179f
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: "npm:^2.0.0"
  checksum: 10c0/a00ca91280e10ee2321de21dda6c168e427df7a63aeaca027ea45e3e466ac5e1a5054199f6547ba1d5a513d3b6b5933457266daaa47f8857fb532a343ee6b5e1
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: "npm:^2.0.0"
  checksum: 10c0/029b5b2c557d831216305c3218e9ff30fa668be31d58dd08088f74c8eabc8362c303e0908b3a93abb25ba10e3a5bfc9cff5eb7fab6ab9cf820e3b160ccb67581
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: 10c0/13bff4fee946e6020b37e76284e95e24aa239c9e34ac4f3451e4c5330fca6f2f962e1d1ab69e4da7940e1fce135107a2b2b98c01d62ea33144350fc89dc5494e
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 10c0/4c9ac55efd356d37ac483bad3093758236ab686192751d1c9daa43188cc5a07b09bd431eb7458a4efd9ca22424bba23253e7b353feb35d7c749ba040de2385fb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001727
  resolution: "caniuse-lite@npm:1.0.30001727"
  checksum: 10c0/f0a441c05d8925d728c2d02ce23b001935f52183a3bf669556f302568fe258d1657940c7ac0b998f92bc41383e185b390279a7d779e6d96a2b47881f56400221
  languageName: node
  linkType: hard

"chalk@npm:4, chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chalk@npm:^2.0.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"charenc@npm:0.0.2, charenc@npm:~0.0.1":
  version: 0.0.2
  resolution: "charenc@npm:0.0.2"
  checksum: 10c0/a45ec39363a16799d0f9365c8dd0c78e711415113c6f14787a22462ef451f5013efae8a28f1c058f81fc01f2a6a16955f7a5fd0cd56247ce94a45349c89877d8
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-launcher@npm:^0.15.2":
  version: 0.15.2
  resolution: "chrome-launcher@npm:0.15.2"
  dependencies:
    "@types/node": "npm:*"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
  bin:
    print-chrome-path: bin/print-chrome-path.js
  checksum: 10c0/fc01abc19af753bb089744362c0de48707f32ea15779407b06fb569e029a6b1fbaa78107165539d768915cf54b5c38594e73d95563c34127873e3826fb43c636
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 10c0/8c5fa3830a2bcee2b53c2e5018226f0141db9ec9f7b1e27a5c57db5512332cde8a0beb769bcbaf0d8775a78afbf2bb841928feca4ea6219638a5b088f9884b46
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0, ci-info@npm:^3.3.0":
  version: 3.8.0
  resolution: "ci-info@npm:3.8.0"
  checksum: 10c0/0d3052193b58356372b34ab40d2668c3e62f1006d5ca33726d1d3c423853b19a85508eadde7f5908496fb41448f465263bf61c1ee58b7832cb6a924537e3863a
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10c0/1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: "npm:^2.0.0"
  checksum: 10c0/09ee6d8b5b818d840bf80ec9561eaf696672197d3a02a7daee2def96d5f52ce6e0bbe7afca754ccf14f04830b5a1b4556273e983507d5029f95bba3016618eda
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10c0/92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0, cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10c0/35229b1bb48647e882104cac374c9a18e34bbf0bace0e2cf03000326b6ca3050d6b59545d91e17bfe3705f4a0e2988787aa5cde6331bf5cbbf0164732cef6492
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10c0/637753615aa24adf0f2d505947a1bb75e63964309034a1cf56ba4b1f30af155201edd38d26ffe26911adaae267a3c138b344a4947d39f5fc1b6d6108125aa758
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10c0/34dead8bee24f5e96f6e7937d711978380647e936a22e76380290e35486afd8634966ce300fc4b74a32f3762c7d4c0303f442c3e259f4ce02374eb0c82834f27
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3, color-name@npm:^1.0.0":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"colorette@npm:^1.0.7":
  version: 1.4.0
  resolution: "colorette@npm:1.4.0"
  checksum: 10c0/4955c8f7daafca8ae7081d672e4bd89d553bd5782b5846d5a7e05effe93c2f15f7e9c0cb46f341b59f579a39fcf436241ff79594899d75d5f3460c03d607fe9e
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"command-exists@npm:^1.2.8":
  version: 1.2.9
  resolution: "command-exists@npm:1.2.9"
  checksum: 10c0/75040240062de46cd6cd43e6b3032a8b0494525c89d3962e280dde665103f8cc304a8b313a5aa541b91da2f5a9af75c5959dc3a77893a2726407a5e9a0234c16
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 10c0/84a76c08fe6cc08c9c93f62ac573d2907d8e79138999312c92d4155bc2325d487d64d13f669b2000c9f8caf70493c1be2dac74fec3c51d5a04f8bc3ae1830bab
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"commander@npm:^9.4.1":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: 10c0/5f7784fbda2aaec39e89eb46f06a999e00224b3763dc65976e05929ec486e174fe9aac2655f03ba6a5e83875bd173be5283dc19309b7c65954701c02025b3c1d
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10c0/33a124960e471c25ee19280c9ce31ccc19574b566dc514fe4f4ca4c34fa8b0b57cf437671f5de380e11353ea9426213fca17687dd2ef03134fea2dbc53809fd6
  languageName: node
  linkType: hard

"component-type@npm:^1.2.1":
  version: 1.2.2
  resolution: "component-type@npm:1.2.2"
  checksum: 10c0/02f895362129da1046c8d3939e88ab7a4caa28d3765cc35b43fa3e7bdad5a9ecb9a5782313f61da7cc1a0aca2cc57d3730e59f4faeb06029e235d7784357b235
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.1":
  version: 1.8.1
  resolution: "compression@npm:1.8.1"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.1.0"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/85114b0b91c16594dc8c671cd9b05ef5e465066a60e5a4ed8b4551661303559a896ed17bb72c4234c04064e078f6ca86a34b8690349499a43f6fc4b844475da4
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"connect@npm:^3.6.5, connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: "npm:2.6.9"
    finalhandler: "npm:1.1.2"
    parseurl: "npm:~1.3.3"
    utils-merge: "npm:1.0.1"
  checksum: 10c0/f120c6116bb16a0a7d2703c0b4a0cd7ed787dc5ec91978097bf62aa967289020a9f41a9cd3c3276a7b92aaa36f382d2cd35fed7138fd466a55c8e9fdbed11ca8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.43.0":
  version: 3.44.0
  resolution: "core-js-compat@npm:3.44.0"
  dependencies:
    browserslist: "npm:^4.25.1"
  checksum: 10c0/5de4b042b8bb232b8390be3079030de5c7354610f136ed3eb91310a44455a78df02cfcf49b2fd05d5a5aa2695460620abf1b400784715f7482ed4770d40a68b2
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.5, cosmiconfig@npm:^5.1.0":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: "npm:^2.0.0"
    is-directory: "npm:^0.3.1"
    js-yaml: "npm:^3.13.1"
    parse-json: "npm:^4.0.0"
  checksum: 10c0/ae9ba309cdbb42d0c9d63dad5c1dfa1c56bb8f818cb8633eea14fd2dbdc9f33393b77658ba96fdabda497bc943afed8c3371d1222afe613c518ba676fa624645
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.1.8
  resolution: "cross-fetch@npm:3.1.8"
  dependencies:
    node-fetch: "npm:^2.6.12"
  checksum: 10c0/4c5e022ffe6abdf380faa6e2373c0c4ed7ef75e105c95c972b6f627c3f083170b6886f19fb488a7fa93971f4f69dcc890f122b0d97f0bf5f41ca1d9a8f58c8af
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.0":
  version: 6.0.6
  resolution: "cross-spawn@npm:6.0.6"
  dependencies:
    nice-try: "npm:^1.0.4"
    path-key: "npm:^2.0.1"
    semver: "npm:^5.5.0"
    shebang-command: "npm:^1.2.0"
    which: "npm:^1.2.9"
  checksum: 10c0/bf61fb890e8635102ea9bce050515cf915ff6a50ccaa0b37a17dc82fded0fb3ed7af5478b9367b86baee19127ad86af4be51d209f64fd6638c0862dca185fe1d
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypt@npm:0.0.2, crypt@npm:~0.0.1":
  version: 0.0.2
  resolution: "crypt@npm:0.0.2"
  checksum: 10c0/adbf263441dd801665d5425f044647533f39f4612544071b1471962209d235042fb703c27eea2795c7c53e1dfc242405173003f83cf4f4761a633d11f9653f18
  languageName: node
  linkType: hard

"crypto-random-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "crypto-random-string@npm:1.0.0"
  checksum: 10c0/0cb4dbbb895656919d1de11ba43829a3527edddb85a9c49c9d4c4eb783d3b03fc9f371cefee62c87082fd8758db2798a52a9cad48a7381826190d3c2cf858e4a
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 10c0/288589b2484fe787f9e146f56c4be90b940018f17af1b152e4dde12309042ff5a2bf69e949aab8b8ac253948381529cc6f3e5a2427b73643a71ff177fa122b37
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 10c0/af205a86c68e0051846ed91eb3e30b4517e1904aac040013ff1d742019b3f9369ba5658ba40901dbbc121186fc4bf0e75a814321cc3e3182fbb2feb81c6d9cb7
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: "npm:^1.0.3"
  checksum: 10c0/8bb042e8f7701a7edadc3cce5ce2d5cf41189631d7e2aed194d5a7059b25776dded2a0466cb9da1d1f3fc6c99dcecb51e45671148d073b8a2a71e34755152e52
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-to-react-native@npm:^3.2.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: "npm:^1.0.0"
    css-color-keywords: "npm:^1.0.0"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 10c0/fde850a511d5d3d7c55a1e9b8ed26b69a8ad4868b3487e36ebfbfc0b96fc34bc977d9cd1d61a289d0c74d3f9a662d8cee297da53d4433bf2e27d6acdff8e1003
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: "npm:2.0.14"
    source-map: "npm:^0.6.1"
  checksum: 10c0/499a507bfa39b8b2128f49736882c0dd636b0cd3370f2c69f4558ec86d269113286b7df469afc955de6a68b0dba00bc533e40022a73698081d600072d5d83c1c
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.1.2":
  version: 3.1.2
  resolution: "csstype@npm:3.1.2"
  checksum: 10c0/32c038af259897c807ac738d9eab16b3d86747c72b09d5c740978e06f067f9b7b1737e1b75e407c7ab1fe1543dc95f20e202b4786aeb1b8d3bdf5d5ce655e6c6
  languageName: node
  linkType: hard

"dag-map@npm:~1.0.0":
  version: 1.0.2
  resolution: "dag-map@npm:1.0.2"
  checksum: 10c0/1b5ee77cbc9caf61178db592ecc8fa8f6905fd4b0571176af74d2fece2332b68c0e9e8275f1c2c76bc1f0c84a9dc973f87233db7a06375bd13254fae9866867f
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"dayjs@npm:^1.8.15":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.0":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"default-gateway@npm:^4.2.0":
  version: 4.2.0
  resolution: "default-gateway@npm:4.2.0"
  dependencies:
    execa: "npm:^1.0.0"
    ip-regex: "npm:^2.1.0"
  checksum: 10c0/2f499b3a9a6c995fd2b4c0d2411256b1899c94e7eacdb895be64e25c301fa8bce8fd3f8152e540669bb178c6a355154c2f86ec23d4ff40ff3b8413d2a59cd86d
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"del@npm:^6.0.0":
  version: 6.1.1
  resolution: "del@npm:6.1.1"
  dependencies:
    globby: "npm:^11.0.1"
    graceful-fs: "npm:^4.2.4"
    is-glob: "npm:^4.0.1"
    is-path-cwd: "npm:^2.2.0"
    is-path-inside: "npm:^3.0.2"
    p-map: "npm:^4.0.0"
    rimraf: "npm:^3.0.2"
    slash: "npm:^3.0.0"
  checksum: 10c0/8a095c5ccade42c867a60252914ae485ec90da243d735d1f63ec1e64c1cfbc2b8810ad69a29ab6326d159d4fddaa2f5bad067808c42072351ec458efff86708f
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"denodeify@npm:^1.2.1":
  version: 1.2.1
  resolution: "denodeify@npm:1.2.1"
  checksum: 10c0/d7e5a974eae4e837f7c70ecb9bdbafae9fbdda1993a86dead1b0ec1d162ed34a9adb2cfbc0bce30d8ccf7a7294aba660862fdce761a0c6157650a0839630d33a
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"deprecated-react-native-prop-types@npm:^2.3.0":
  version: 2.3.0
  resolution: "deprecated-react-native-prop-types@npm:2.3.0"
  dependencies:
    "@react-native/normalize-color": "npm:*"
    invariant: "npm:*"
    prop-types: "npm:*"
  checksum: 10c0/a4bf78dde109cd1a706206fa6e06f7c44e70bc327b990f0f7f936d5d0b8a2372bc78e09979fb2e84bbc5233774aca4165b3ecd17a74765ccc11cead4827380ca
  languageName: node
  linkType: hard

"deprecated-react-native-prop-types@npm:^4.0.0":
  version: 4.1.0
  resolution: "deprecated-react-native-prop-types@npm:4.1.0"
  dependencies:
    "@react-native/normalize-colors": "npm:*"
    invariant: "npm:*"
    prop-types: "npm:*"
  checksum: 10c0/fdfcaebb0d0b9830ade32e48747a63b85429999373c46c3673f086b4eba1aafd9241e88b6f8484da8dbcec64df4b33cb469128fa743099f7ab77e3a5ce4d220d
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.0":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10c0/f735074d66dd759b36b158fa26e9d00c9388ee0e8c9b16af941c38f014a37fc80782de83afefd621681b19ac0501034b4f1c4a3bff5caa1b8667f0212b5e124c
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.1.0
  resolution: "domutils@npm:3.1.0"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/342d64cf4d07b8a0573fb51e0a6312a88fb520c7fefd751870bf72fa5fc0f2e0cb9a3958a573610b1d608c6e2a69b8e9b4b40f0bfb8f87a71bce4f180cca1887
  languageName: node
  linkType: hard

"dotenv-expand@npm:~11.0.6":
  version: 11.0.6
  resolution: "dotenv-expand@npm:11.0.6"
  dependencies:
    dotenv: "npm:^16.4.4"
  checksum: 10c0/e22891ec72cb926d46d9a26290ef77f9cc9ddcba92d2f83d5e6f3a803d1590887be68e25b559415d080053000441b6f63f5b36093a565bb8c5c994b992ae49f2
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.4, dotenv@npm:~16.4.5":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 10c0/48d92870076832af0418b13acd6e5a5a3e83bb00df690d9812e94b24aff62b88ade955ac99a05501305b8dc8f1b0ee7638b18493deb6fe93d680e5220936292f
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.187
  resolution: "electron-to-chromium@npm:1.5.187"
  checksum: 10c0/c83153010b786deac926fb128e0e0a68202312a25b4896892bcf166acb2ccb6357ec918ba38d44e16294cd85bf7e345198e1809e1848295221a8753beb658241
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/b0701c92a10b89afb1cb45bf54a5292c6f008d744eb4382fa559d54775ff31617d1d7bc3ef617575f552e24fad2c7c1a1835948c66b3f3a4be0a6c1f35c883d8
  languageName: node
  linkType: hard

"entities@npm:^4.2.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"env-editor@npm:^0.4.1":
  version: 0.4.2
  resolution: "env-editor@npm:0.4.2"
  checksum: 10c0/edb33583b0ae5197535905cbcefca424796f6afec799604f7578428ee523245edcd7df48d582fdab67dbcc697ed39070057f512e72f94c91ceefdcb432f5eadb
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"envinfo@npm:^7.10.0":
  version: 7.14.0
  resolution: "envinfo@npm:7.14.0"
  bin:
    envinfo: dist/cli.js
  checksum: 10c0/059a031eee101e056bd9cc5cbfe25c2fab433fe1780e86cf0a82d24a000c6931e327da6a8ffb3dce528a24f83f256e7efc0b36813113eff8fdc6839018efe327
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"errorhandler@npm:^1.5.1":
  version: 1.5.1
  resolution: "errorhandler@npm:1.5.1"
  dependencies:
    accepts: "npm:~1.3.7"
    escape-html: "npm:~1.0.3"
  checksum: 10c0/58568c7eec3f4de5dc49e4385a50af66b76759b3463a86e4a85e05c4f7a5348f51d3d23af51c3a23eceef6278045d0a47d975da11bdaaf92d1d783dc677e980e
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-negative-zero: "npm:^2.0.3"
    is-regex: "npm:^1.2.1"
    is-set: "npm:^2.0.3"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    stop-iteration-iterator: "npm:^1.1.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10c0/b256e897be32df5d382786ce8cce29a1dd8c97efbab77a26609bd70f2ed29fbcfc7a31758cb07488d532e7ccccdfca76c1118f2afe5a424cdc05ca007867c318
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10c0/2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0, event-target-shim@npm:^5.0.1":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"exec-async@npm:^2.2.0":
  version: 2.2.0
  resolution: "exec-async@npm:2.2.0"
  checksum: 10c0/9c70693a3d9f53e19cc8ecf26c3b3fc7125bf40051a71cba70d71161d065a6091d3ab1924c56ac1edd68cb98b9fbef29f83e45dcf67ee6b6c4826e0f898ac039
  languageName: node
  linkType: hard

"execa@npm:^1.0.0":
  version: 1.0.0
  resolution: "execa@npm:1.0.0"
  dependencies:
    cross-spawn: "npm:^6.0.0"
    get-stream: "npm:^4.0.0"
    is-stream: "npm:^1.1.0"
    npm-run-path: "npm:^2.0.0"
    p-finally: "npm:^1.0.0"
    signal-exit: "npm:^3.0.0"
    strip-eof: "npm:^1.0.0"
  checksum: 10c0/cc71707c9aa4a2552346893ee63198bf70a04b5a1bc4f8a0ef40f1d03c319eae80932c59191f037990d7d102193e83a38ec72115fff814ec2fb3099f3661a590
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"expo-asset@npm:~10.0.10":
  version: 10.0.10
  resolution: "expo-asset@npm:10.0.10"
  dependencies:
    expo-constants: "npm:~16.0.0"
    invariant: "npm:^2.2.4"
    md5-file: "npm:^3.2.3"
  peerDependencies:
    expo: "*"
  checksum: 10c0/aed3164cee4483e47fa56c8898384769d60ebb3f94553f7ad2a33a8902d73a1379aee3fc51833c8f0a4a59979ed842ba079e52c8e1903104b1ad312ad90fe1d1
  languageName: node
  linkType: hard

"expo-blur@npm:~13.0.2":
  version: 13.0.3
  resolution: "expo-blur@npm:13.0.3"
  peerDependencies:
    expo: "*"
  checksum: 10c0/632aea3dda468e0331c8aa9d63aae39c5d9cba7276e0f246da8fc8009a9f9033aa20aab249814d45056057473c6573dd417198e315682997e3a465f902a4c520
  languageName: node
  linkType: hard

"expo-build-properties@npm:~0.12.3":
  version: 0.12.5
  resolution: "expo-build-properties@npm:0.12.5"
  dependencies:
    ajv: "npm:^8.11.0"
    semver: "npm:^7.6.0"
  peerDependencies:
    expo: "*"
  checksum: 10c0/1418c94d97cc5c5ecb5ec947ca7eb218bb3d14a5c7127c6acaf9f763af5060df6e581e90b7ec210cdf4fb16d676f58500414127375b2e169646c66b5bd79e9cc
  languageName: node
  linkType: hard

"expo-clipboard@npm:~6.0.3":
  version: 6.0.3
  resolution: "expo-clipboard@npm:6.0.3"
  peerDependencies:
    expo: "*"
  checksum: 10c0/7ac2018c74bae620eca888ae973658405c44ffd17168a4c32b20c2a48c080782dd7dd4e81e7081f3f41b3b83e67e367af001fcd808ebc2f733f90b0268842bf1
  languageName: node
  linkType: hard

"expo-constants@npm:~16.0.0, expo-constants@npm:~16.0.2":
  version: 16.0.2
  resolution: "expo-constants@npm:16.0.2"
  dependencies:
    "@expo/config": "npm:~9.0.0"
    "@expo/env": "npm:~0.3.0"
  peerDependencies:
    expo: "*"
  checksum: 10c0/3a51ef1d4de7e7a86d8ee7ef7b0e0edb6cbde981e1e09540e5d7a5dfec3327cb805df06f10614ebf87a158b01a274f63eef9f573e87d7cf1040ffd7168c8a5d1
  languageName: node
  linkType: hard

"expo-contacts@npm:~13.0.4":
  version: 13.0.5
  resolution: "expo-contacts@npm:13.0.5"
  peerDependencies:
    expo: "*"
  checksum: 10c0/612ec89611aa2f99d585347081479a22efe8bea692e4827faf36a292ad57eae940697a633c9378918d07c8208bbf48f0102f2248d3a1699e17c6cbde6a539d13
  languageName: node
  linkType: hard

"expo-file-system@npm:~17.0.1":
  version: 17.0.1
  resolution: "expo-file-system@npm:17.0.1"
  peerDependencies:
    expo: "*"
  checksum: 10c0/902913301afd11a2d91b1b9bf053924bfb70f868050e6893854052e589afcc3cb09f0d4cb15194313c6d52dbd7d17ec258c86fc9f2303d1d29d1d745aa6c98d5
  languageName: node
  linkType: hard

"expo-font@npm:~12.0.10, expo-font@npm:~12.0.7":
  version: 12.0.10
  resolution: "expo-font@npm:12.0.10"
  dependencies:
    fontfaceobserver: "npm:^2.1.0"
  peerDependencies:
    expo: "*"
  checksum: 10c0/49b7da4c5099f74a3641841e8a684a15b743e0d63bfc60355c7b2cf0a5b33b4321b0657c282126795da5ef53778b4d29a765dc9d08fe395e4bc801662305dee8
  languageName: node
  linkType: hard

"expo-keep-awake@npm:~13.0.2":
  version: 13.0.2
  resolution: "expo-keep-awake@npm:13.0.2"
  peerDependencies:
    expo: "*"
  checksum: 10c0/8548e46991739f42456428141b574c9d83ef77f2a79f371b5c6c1b77364759d4a993af8d40c027a904b5870d41165c99e3e4a8fea93316853819ba16fac0d692
  languageName: node
  linkType: hard

"expo-linear-gradient@npm:~13.0.2":
  version: 13.0.2
  resolution: "expo-linear-gradient@npm:13.0.2"
  peerDependencies:
    expo: "*"
  checksum: 10c0/1643ff61092e13efb573880defe04e28f4f91cdbf4ba1c762d6694a504dadfa4365ce786dae312f5baeacc22aa3fd048f32cd2d4471a922ac4c42b1dd3a0129b
  languageName: node
  linkType: hard

"expo-modules-autolinking@npm:1.11.3":
  version: 1.11.3
  resolution: "expo-modules-autolinking@npm:1.11.3"
  dependencies:
    chalk: "npm:^4.1.0"
    commander: "npm:^7.2.0"
    fast-glob: "npm:^3.2.5"
    find-up: "npm:^5.0.0"
    fs-extra: "npm:^9.1.0"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
  bin:
    expo-modules-autolinking: bin/expo-modules-autolinking.js
  checksum: 10c0/7b37f2405f64b2a606c826e432b588dba395e5f8587e5ea4abf385a87b18e508e9eba7a9d41299d5a6920c58f891e52193ad50b3eb751f708cdfcd24cadafb63
  languageName: node
  linkType: hard

"expo-modules-core@npm:1.12.26":
  version: 1.12.26
  resolution: "expo-modules-core@npm:1.12.26"
  dependencies:
    invariant: "npm:^2.2.4"
  checksum: 10c0/02fd20d52e15cb8c34f0f652512e7fe5ba66fd353a2fbd05888f22bfa4f3de4b699724c37393b415b1336f9ce9691e67342c9fc9ccded4f8806a726b0a711d3c
  languageName: node
  linkType: hard

"expo-splash-screen@npm:~0.27.5":
  version: 0.27.7
  resolution: "expo-splash-screen@npm:0.27.7"
  dependencies:
    "@expo/prebuild-config": "npm:7.0.9"
  peerDependencies:
    expo: "*"
  checksum: 10c0/059ab08530f99e3ac124c0c18d6b42dc3333181b27e2d94b3d8ceb8bfa09221ebd1ee3577b98fc78a36a52b8eec36730292e72eb7257eae100d30b4a288480c3
  languageName: node
  linkType: hard

"expo-status-bar@npm:~1.12.1":
  version: 1.12.1
  resolution: "expo-status-bar@npm:1.12.1"
  checksum: 10c0/fecd1c1843ef1fadf3007f54e1c778d06cbbe396c9ba7cbbfde88a1eb215847ab39e68b1f32d0d46a906a1abcb8821aa33eefed66d0c4a858a5faf6b6e556416
  languageName: node
  linkType: hard

"expo@npm:~51.0.39":
  version: 51.0.39
  resolution: "expo@npm:51.0.39"
  dependencies:
    "@babel/runtime": "npm:^7.20.0"
    "@expo/cli": "npm:0.18.31"
    "@expo/config": "npm:9.0.4"
    "@expo/config-plugins": "npm:8.0.11"
    "@expo/metro-config": "npm:0.18.11"
    "@expo/vector-icons": "npm:^14.0.3"
    babel-preset-expo: "npm:~11.0.15"
    expo-asset: "npm:~10.0.10"
    expo-file-system: "npm:~17.0.1"
    expo-font: "npm:~12.0.10"
    expo-keep-awake: "npm:~13.0.2"
    expo-modules-autolinking: "npm:1.11.3"
    expo-modules-core: "npm:1.12.26"
    fbemitter: "npm:^3.0.0"
    whatwg-url-without-unicode: "npm:8.0.0-3"
  bin:
    expo: bin/cli
  checksum: 10c0/2a2112653e6b2df968a299f3dd871919110ec8d07e12faef4db0ae2b8132afa47e6e228b6c2afdda3a30006e13faa23b93a1fdb41e1c84104018ba97d479bd4e
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.5, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10c0/42baad7b9cd40b63e42039132bde27ca2cb3a4950d0a0f9abe4639ea1aa9d3e3b40f98b1fe31cbc0cc17b664c9ea7447d911a152fa34ec5b72977b125a6fc845
  languageName: node
  linkType: hard

"fast-loops@npm:^1.1.3":
  version: 1.1.3
  resolution: "fast-loops@npm:1.1.3"
  checksum: 10c0/ba71c001704c44a617053ed34b1a8c0d2ed9723022eb7b93c98299d9862f93213609b32c9daec7d606625ab318769d11da8bb06e9ddd9c28e3bda1249fb6e36d
  languageName: node
  linkType: hard

"fast-xml-parser@npm:^4.0.12, fast-xml-parser@npm:^4.2.4":
  version: 4.5.3
  resolution: "fast-xml-parser@npm:4.5.3"
  dependencies:
    strnum: "npm:^1.1.1"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/bf9ccadacfadc95f6e3f0e7882a380a7f219cf0a6f96575149f02cb62bf44c3b7f0daee75b8ff3847bcfd7fbcb201e402c71045936c265cf6d94b141ec4e9327
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/5ce4f83afa5f88c9379e67906b4d31bc7694a30826d6cc8d0f0473c966929017fda65c2174b0ec89f064ede6ace6c67f8a4fe04cef42119b6a55b0d465554c24
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10c0/feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"fbemitter@npm:^3.0.0":
  version: 3.0.0
  resolution: "fbemitter@npm:3.0.0"
  dependencies:
    fbjs: "npm:^3.0.0"
  checksum: 10c0/f130dd8e15dc3fc6709a26586b7a589cd994e1d1024b624f2cc8ef1b12401536a94bb30038e68150a24f9ba18863e9a3fe87941ade2c87667bfbd17f4848d5c7
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 10c0/dfb64116b125a64abecca9e31477b5edb9a2332c5ffe74326fe36e0a72eef7fc8a49b86adf36c2c293078d79f4524f35e80f5e62546395f53fb7c9e69821f54f
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.0":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: "npm:^3.1.5"
    fbjs-css-vars: "npm:^1.0.0"
    loose-envify: "npm:^1.0.0"
    object-assign: "npm:^4.1.0"
    promise: "npm:^7.1.1"
    setimmediate: "npm:^1.0.5"
    ua-parser-js: "npm:^1.0.35"
  checksum: 10c0/66d0a2fc9a774f9066e35ac2ac4bf1245931d27f3ac287c7d47e6aa1fc152b243c2109743eb8f65341e025621fb51a12038fadb9fd8fda2e3ddae04ebab06f91
  languageName: node
  linkType: hard

"fdir@npm:^6.5.0":
  version: 6.5.0
  resolution: "fdir@npm:6.5.0"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/e345083c4306b3aed6cb8ec551e26c36bab5c511e99ea4576a16750ddc8d3240e63826cc624f5ae17ad4dc82e68a253213b60d556c11bfad064b7607847ed07f
  languageName: node
  linkType: hard

"fetch-retry@npm:^4.1.1":
  version: 4.1.1
  resolution: "fetch-retry@npm:4.1.1"
  checksum: 10c0/f55cdc82d096e8ef92f92218a8379a01d56cc01726a0ac554845eb943758ceca8be2619682678adfbff88ecb4d97269375200af7ca94a726a8195781aa4c2f49
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/7cdad7d426ffbaadf45aeb5d15ec675bbd77f7597ad5399e3d2766987ed20bda24d5fac64b3ee79d93276f5865608bb22344a26b9b1ae6c4d00bd94bf611623f
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10c0/071e0886b2b50238ca5026c5bbf58c26a7c1a1f720773b8c7813d16ba93d0200de977af14ac143c5ac18f666b2cfc83073f3a5fe6a4e996c49e0863d5500fccf
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/6a96e1f5caab085628c11d9fdceb82ba608d5e426c6913d4d918409baa271037a47f28fbba73279e8ad614f0b8fa71ea791d265e408d760793829edd8c2f4584
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^2.0.0"
    pkg-dir: "npm:^3.0.0"
  checksum: 10c0/556117fd0af14eb88fb69250f4bba9e905e7c355c6136dff0e161b9cbd1f5285f761b778565a278da73a130f42eccc723d7ad4c002ae547ed1d698d39779dabb
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: "npm:^3.0.0"
  checksum: 10c0/2c2e7d0a26db858e2f624f39038c74739e38306dee42b45f404f770db357947be9d0d587f1cac72d20c114deb38aa57316e879eb0a78b17b46da7dab0a3bd6e3
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0, find-up@npm:~5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"find-yarn-workspace-root@npm:~2.0.0":
  version: 2.0.0
  resolution: "find-yarn-workspace-root@npm:2.0.0"
  dependencies:
    micromatch: "npm:^4.0.2"
  checksum: 10c0/b0d3843013fbdaf4e57140e0165889d09fa61745c9e85da2af86e54974f4cc9f1967e40f0d8fc36a79d53091f0829c651d06607d552582e53976f3cd8f4e5689
  languageName: node
  linkType: hard

"flow-enums-runtime@npm:^0.0.6":
  version: 0.0.6
  resolution: "flow-enums-runtime@npm:0.0.6"
  checksum: 10c0/f0b9ca52dbf9cf30264ebf1af034ac7b80fb5e5ef009efc789b89a90aa17349a3ff5672b3b27c6eb89d5e02808fc0dfb7effbfc5a793451694d6cce48774d51e
  languageName: node
  linkType: hard

"flow-parser@npm:0.*":
  version: 0.275.0
  resolution: "flow-parser@npm:0.275.0"
  checksum: 10c0/47f30339022b00c6037c5281d5c6c9995b8f174677d0b7e950fa4661e7ab71573031e9c70e567783f790a1216fcbeac3cf8c4639fda4a0af2b2035b490fc17ed
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.0":
  version: 1.15.3
  resolution: "follow-redirects@npm:1.15.3"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/915a2cf22e667bdf47b1a43cc6b7dce14d95039e9bbf9a24d0e739abfbdfa00077dd43c86d4a7a19efefcc7a99af144920a175eedc3888d268af5df67c272ee5
  languageName: node
  linkType: hard

"fontfaceobserver@npm:^2.1.0":
  version: 2.3.0
  resolution: "fontfaceobserver@npm:2.3.0"
  checksum: 10c0/9b539d5021757d3ed73c355bdb839296d6654de473a992aa98993ef46d951f0361545323de68f6d70c5334d7e3e9f409c1ae7a03c168b00cb0f6c5dea6c77bfa
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^3.0.1":
  version: 3.0.4
  resolution: "form-data@npm:3.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.35"
  checksum: 10c0/2451043b3e931653ce9690ba051b0bf1b5855a63029279bd7bdf8d02e4b5b42f4582b23ed3637df27a0d21bac2013c37d165ec9486e1af2470c13114aee83acc
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/cb6f3ac49180be03ff07ba3ff125f9eba2ff0b277fb33c7fc47569fc5e616882c5b1c69b9904c4c4187e97dd0419dd03b134174756f296dec62041e6527e2c6e
  languageName: node
  linkType: hard

"freeport-async@npm:2.0.0":
  version: 2.0.0
  resolution: "freeport-async@npm:2.0.0"
  checksum: 10c0/421828d1a689695b6c8122d310fd8941af99ebe0b5793e3f8d49aa5923ce580b6c4dd6b7470d46983e60839c302f6c793a8541dbab80817396cdde2b04c83c90
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:9.0.0":
  version: 9.0.0
  resolution: "fs-extra@npm:9.0.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^1.0.0"
  checksum: 10c0/c7f8903b5939a585d16c064142929a9ad12d63084009a198da37bd2c49095b938c8f9a88f8378235dafd5312354b6e872c0181f97f820095fb3539c9d5fe6cd0
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0, fs-extra@npm:~8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10c0/259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0, fs-extra@npm:^9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^4.0.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 10c0/294d876f667694a5ca23f0ca2156de67da950433b6fb53024833733975d32582896dbc7f257842d331809979efccf04d5e0b6b75ad4d45744c45f193fd497539
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"getenv@npm:^1.0.0":
  version: 1.0.0
  resolution: "getenv@npm:1.0.0"
  checksum: 10c0/9661c5996c7622e12eab1d23448474ae51dbec6f8862eed903ebaa864dcd332895441c23d962e3ff5c180a9e3dff6cb1f569a115e1447db4acb52af2d880d655
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob@npm:7.1.6, glob@npm:^7.1.1, glob@npm:^7.1.3":
  version: 7.1.6
  resolution: "glob@npm:7.1.6"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.0.4"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/2575cce9306ac534388db751f0aa3e78afedb6af8f3b529ac6b2354f66765545145dba8530abf7bff49fb399a047d3f9b6901c38ee4c9503f592960d9af67763
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.4.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.7, glob@npm:^7.2.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"globby@npm:^11.0.1":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.3, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphql-tag@npm:^2.10.1":
  version: 2.12.6
  resolution: "graphql-tag@npm:2.12.6"
  dependencies:
    tslib: "npm:^2.1.0"
  peerDependencies:
    graphql: ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10c0/7763a72011bda454ed8ff1a0d82325f43ca6478e4ce4ab8b7910c4c651dd00db553132171c04d80af5d5aebf1ef6a8a9fd53ccfa33b90ddc00aa3d4be6114419
  languageName: node
  linkType: hard

"graphql@npm:15.8.0":
  version: 15.8.0
  resolution: "graphql@npm:15.8.0"
  checksum: 10c0/30cc09b77170a9d1ed68e4c017ec8c5265f69501c96e4f34f8f6613f39a886c96dd9853eac925f212566ed651736334c8fe24ceae6c44e8d7625c95c3009a801
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hermes-estree@npm:0.19.1":
  version: 0.19.1
  resolution: "hermes-estree@npm:0.19.1"
  checksum: 10c0/98c79807c15146c745aca7a9c74b9f1ba20a463c8b9f058caed9b3f2741fc4a8609e7e4c06d163f67d819db35cb6871fc7b25085bb9a084bc53d777f67d9d620
  languageName: node
  linkType: hard

"hermes-estree@npm:0.23.1":
  version: 0.23.1
  resolution: "hermes-estree@npm:0.23.1"
  checksum: 10c0/59ca9f3980419fcf511a172f0ee9960d86c8ba44ea8bc13d3bd0b6208e9540db1a0a9e46b0e797151f11b0e8e33b2bf850907aef4a5c9ac42c53809cefefc405
  languageName: node
  linkType: hard

"hermes-parser@npm:0.19.1":
  version: 0.19.1
  resolution: "hermes-parser@npm:0.19.1"
  dependencies:
    hermes-estree: "npm:0.19.1"
  checksum: 10c0/940ccef90673b8e905016332d2660ae00ad747e2d32c694a52dce4ea220835dc1bae299554a7a8eeccb449561065bd97f3690363c087fbf69ad7cbff2deeec35
  languageName: node
  linkType: hard

"hermes-parser@npm:0.23.1":
  version: 0.23.1
  resolution: "hermes-parser@npm:0.23.1"
  dependencies:
    hermes-estree: "npm:0.23.1"
  checksum: 10c0/56907e6136d2297543922dd9f8ee27378ef010c11dc1e0b4a0866faab2c527613b0edcda5e1ebc0daa0ca1ae6528734dfc479e18267aabe4dce0c7198217fd97
  languageName: node
  linkType: hard

"hermes-profile-transformer@npm:^0.0.6":
  version: 0.0.6
  resolution: "hermes-profile-transformer@npm:0.0.6"
  dependencies:
    source-map: "npm:^0.7.3"
  checksum: 10c0/d772faa712f97ec009cb8de1f6b2dc26af491d1baaea92af7649fbb9cafd60a9c7a499de32d23ba7606e501147bfb2daf14e477c967f11e3de8a1e41ecf626c7
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10c0/fe0889169e845d738b59b64badf5e55fa3cf20454f9203d1eb088df322d49d4318df774828e789898dcb280e8a5521bb59b3203385662ca5e9218a6ca5820e74
  languageName: node
  linkType: hard

"hosted-git-info@npm:^3.0.2":
  version: 3.0.8
  resolution: "hosted-git-info@npm:3.0.8"
  dependencies:
    lru-cache: "npm:^6.0.0"
  checksum: 10c0/af1392086ab3ab5576aa81af07be2f93ee1588407af18fd9752eb67502558e6ea0ffdd4be35ac6c8bef12fb9017f6e7705757e21b10b5ce7798da9106c9c0d9d
  languageName: node
  linkType: hard

"hosted-git-info@npm:^7.0.0":
  version: 7.0.2
  resolution: "hosted-git-info@npm:7.0.2"
  dependencies:
    lru-cache: "npm:^10.0.1"
  checksum: 10c0/b19dbd92d3c0b4b0f1513cf79b0fc189f54d6af2129eeb201de2e9baaa711f1936929c848b866d9c8667a0f956f34bf4f07418c12be1ee9ca74fd9246335ca1f
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.1":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.0.4
  resolution: "hyphenate-style-name@npm:1.0.4"
  checksum: 10c0/b19c3e2cd1dc426f6f893752fec08140abf79058a1b6238422e45373ed81389f02e1a2ba2ef4e9b2430d4e900a0f5ba12307de82320604e81ac1b722abd2ee62
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"image-size@npm:^1.0.2":
  version: 1.2.1
  resolution: "image-size@npm:1.2.1"
  dependencies:
    queue: "npm:6.0.2"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/f8b3c19d4476513f1d7e55c3e6db80997b315444743e2040d545cbcaee59be03d2eb40c46be949a8372697b7003fdb0c04925d704390a7f606bc8181e25c0ed4
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: "npm:^2.0.0"
    resolve-from: "npm:^3.0.0"
  checksum: 10c0/116c55ee5215a7839062285b60df85dbedde084c02111dc58c1b9d03ff7876627059f4beb16cdc090a3db21fea9022003402aa782139dc8d6302589038030504
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^6.0.1":
  version: 6.0.4
  resolution: "inline-style-prefixer@npm:6.0.4"
  dependencies:
    css-in-js-utils: "npm:^3.1.0"
    fast-loops: "npm:^1.1.3"
  checksum: 10c0/d3d42bf0c48d621ea4bcfb077b5d370b106995422300a3a472674f96c9b489d96b4aac6f29dea3bb26ff2dfd7293e4752098bc2b53407769eafdb66c6c4c1764
  languageName: node
  linkType: hard

"internal-ip@npm:4.3.0":
  version: 4.3.0
  resolution: "internal-ip@npm:4.3.0"
  dependencies:
    default-gateway: "npm:^4.2.0"
    ipaddr.js: "npm:^1.9.0"
  checksum: 10c0/c0ad0b95981c8f21a2d4f115212af38c894a6a6d0a2a3cac4d73d1b5beb214fdfce7b5e66f087e8d575977d4df630886914412d1bc9c2678e5870210154ad65b
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"intl-messageformat@npm:^10.1.0":
  version: 10.5.4
  resolution: "intl-messageformat@npm:10.5.4"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.17.2"
    "@formatjs/fast-memoize": "npm:2.2.0"
    "@formatjs/icu-messageformat-parser": "npm:2.7.0"
    tslib: "npm:^2.4.0"
  checksum: 10c0/109007787d270e8fdfce3347079b4d6277e1e2ba252170c3a31ad9cd36f0065eb15861db02c1056fdcff1b40b366d0407365ae2b5748a73f633ef883ace7607f
  languageName: node
  linkType: hard

"invariant@npm:*, invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^10.0.1":
  version: 10.0.1
  resolution: "ip-address@npm:10.0.1"
  checksum: 10c0/1634d79dae18394004775cb6d699dc46b7c23df6d2083164025a2b15240c1164fccde53d0e08bd5ee4fc53913d033ab6b5e395a809ad4b956a940c446e948843
  languageName: node
  linkType: hard

"ip-regex@npm:^2.1.0":
  version: 2.1.0
  resolution: "ip-regex@npm:2.1.0"
  checksum: 10c0/3ce2d8307fa0373ca357eba7504e66e73b8121805fd9eba6a343aeb077c64c30659fa876b11ac7a75635b7529d2ce87723f208a5b9d51571513b5c68c0cc1541
  languageName: node
  linkType: hard

"ipaddr.js@npm:^1.9.0":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-buffer@npm:~1.1.1, is-buffer@npm:~1.1.6":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 10c0/ae18aa0b6e113d6c490ad1db5e8df9bdb57758382b313f5a22c9c61084875c6396d50bbf49315f5b1926d142d74dfb8d31b40d993a383e0a158b15fea7a82234
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: 10c0/1c39c7d1753b04e9483b89fb88908b8137ab4743b6f481947e97ccf93ecb384a814c8d3f0b95b082b149c5aa19c3e9e4464e2791d95174bce95998c26bb1974b
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-extglob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-extglob@npm:1.0.0"
  checksum: 10c0/1ce5366d19958f36069a45ca996c1e51ab607f42a01eb0505f0ccffe8f9c91f5bcba6e971605efd8b4d4dfd0111afa3c8df3e1746db5b85b9a8f933f5e7286b7
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: 10c0/e58f3e4a601fc0500d8b2677e26e9fe0cd450980e66adb29d85b6addf7969731e38f8e43ed2ec868a09c101a55ac3d8b78902209269f38c5286bc98f5bc1b4d9
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-glob@npm:2.0.1"
  dependencies:
    is-extglob: "npm:^1.0.0"
  checksum: 10c0/ef156806af0924983325c9218a8b8a838fa50e1a104ed2a11fe94829a5b27c1b05a4c8cf98d96cb3a7fea539c21f14ae2081e1a248f3d5a9eea62f2d4e9f8b0c
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 10c0/dd47904dbf286cd20aa58c5192161be1a67138485b9836d5a70433b21a45442e9611b8498b8ab1f839fc962c7620667a50535fdfb4a6bc7989b8858645c06b4d
  languageName: node
  linkType: hard

"is-invalid-path@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-invalid-path@npm:0.1.0"
  dependencies:
    is-glob: "npm:^2.0.0"
  checksum: 10c0/9f7f74825ddcbd70ceb0aca1155d2961f3767a7a0f1351c255d25047cc7dece161b755d0698aaf8f201693d96ea12e04b4afa00ee9b4f8f47ab5ec2adbe96df8
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: 10c0/bcdcf6b8b9714063ffcfa9929c575ac69bfdabb8f4574ff557dfc086df2836cf07e3906f5bbc4f2a5c12f8f3ba56af640c843cdfc74da8caed86c7c7d66fd08e
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 10c0/afce71533a427a759cd0329301c18950333d7589533c2c90205bd3fdcf7b91eb92d1940493190567a433134d2128ec9325de2fd281e05be1920fbee9edd22e0a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10c0/cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: 10c0/e5c9814cdaa627a9ad0a0964ded0e0491bfd9ace405c49a5d63c88b30a162f1512c069d5b80997893c4d0181eadc3fed02b4ab4b81059aba5620bfcdfdeb9c53
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 10c0/b8ae7971e78d2e8488d15f804229c6eed7ed36a28f8807a1815938771f4adff0e705218b7dab968270433f67103e4fef98062a0beea55d64835f705ee72c7002
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 10c0/00cbe3455c3756be68d2542c416cab888aebd5012781d6819749fefb15162ff23e38501fe681b3d751c73e8ff561ac09a5293eba6f58fdf0178462ce6dcb3453
  languageName: node
  linkType: hard

"is-valid-path@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-valid-path@npm:0.1.1"
  dependencies:
    is-invalid-path: "npm:^0.1.0"
  checksum: 10c0/05c3533b8d98ac469bec9849e6ee73a07e1f9857e2043c75a9a45d21bae5e11fafb625808d7bd1aaf5cc63e842876c636f9888388a959ee9c33975c7b603c6ba
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-wsl@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-wsl@npm:1.1.0"
  checksum: 10c0/7ad0012f21092d6f586c7faad84755a8ef0da9b9ec295e4dc82313cce4e1a93a3da3c217265016461f9b141503fe55fa6eb1fd5457d3f05e8d1bdbb48e50c13a
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.6.3":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/61f04fec077f8b1b5c1a633e3612fc0c9aa79a0ab7b05600683428f1e01a4d35346c474bde6f439f9fcc1a4aa9a2861ff852d079a43ab64b02105d1004b2592b
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10c0/552e7a97a983d3c2d4e412a44eb7de0430ff773dd99f7500962c268d6dfbfa431d7d08f919c9d960530e5f7f78eb47f267ad9b318265e5092b3ff9ede0db7c2b
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/850ae35477f59f3e6f27efac5215f706296e2104af39232bb14e5403e067992afb5c015e87a9243ec4d9df38525ef1ca663af9f2f4766aa116f127247008bd22
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/7b9f8349ee87695a309fe15c46a74ab04c853369e5c40952d68061d9dc3159a0f0ed73e215f81b07ee97a9faaf10aebe5877a9d6255068a0977eae6a9ff1d5ac
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10c0/bc55a8f49fdbb8f51baf31d2a4f312fb66c9db1483b82f602c9c990e659cdd7ec529c8e916d5a89452ecbcfae4949b21b40a7a59d4ffc0cd813a973ab08c8150
  languageName: node
  linkType: hard

"jest-validate@npm:^29.6.3":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/a20b930480c1ed68778c739f4739dce39423131bc070cd2505ddede762a5570a256212e9c2401b7ae9ba4d7b7c0803f03c5b8f1561c62348213aba18d9dbece2
  languageName: node
  linkType: hard

"jest-worker@npm:^29.6.3":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/5570a3a005b16f46c131968b8a5b56d291f9bbb85ff4217e31c80bd8a02e7de799e59a54b95ca28d5c302f248b54cbffde2d177c2f0f52ffcee7504c6eabf660
  languageName: node
  linkType: hard

"jimp-compact@npm:0.16.1":
  version: 0.16.1
  resolution: "jimp-compact@npm:0.16.1"
  checksum: 10c0/2d73bb927d840ce6dc093d089d770eddbb81472635ced7cad1d7c4545d8734aecf5bd3dedf7178a6cfab4d06c9d6cbbf59e5cb274ed99ca11cd4835a6374f16c
  languageName: node
  linkType: hard

"joi@npm:^17.2.1":
  version: 17.13.3
  resolution: "joi@npm:17.13.3"
  dependencies:
    "@hapi/hoek": "npm:^9.3.0"
    "@hapi/topo": "npm:^5.1.0"
    "@sideway/address": "npm:^4.1.5"
    "@sideway/formula": "npm:^3.0.1"
    "@sideway/pinpoint": "npm:^2.0.0"
  checksum: 10c0/9262aef1da3f1bec5b03caf50c46368899fe03b8ff26cbe3d53af4584dd1049079fc97230bbf1500b6149db7cc765b9ee45f0deb24bb6fc3fa06229d7148c17f
  languageName: node
  linkType: hard

"join-component@npm:^1.1.0":
  version: 1.1.0
  resolution: "join-component@npm:1.1.0"
  checksum: 10c0/7319cb1ca6ffc514d82ac1b965c4e6cd6bf852adec1e7833bd8613e17f4965e78e2653c8de75a1fe51d9a2cae36af3298008df4079cfd903ef3ecbd231fe11c1
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsc-android@npm:^250231.0.0":
  version: 250231.0.0
  resolution: "jsc-android@npm:250231.0.0"
  checksum: 10c0/518ddbc9d41eb5f4f8a30244382044c87ce02756416866c4e129ae6655feb0bab744cf9d590d240916b005c3632554c7c33d388a84dc6d3e83733d0e8cee5c2f
  languageName: node
  linkType: hard

"jsc-safe-url@npm:^0.2.2, jsc-safe-url@npm:^0.2.4":
  version: 0.2.4
  resolution: "jsc-safe-url@npm:0.2.4"
  checksum: 10c0/429bd645f8a35938f08f5b01c282e5ef55ed8be30a9ca23517b7ca01dcbf84b4b0632042caceab50f8f5c0c1e76816fe3c74de3e59be84da7f89ae1503bd3c68
  languageName: node
  linkType: hard

"jscodeshift@npm:^0.14.0":
  version: 0.14.0
  resolution: "jscodeshift@npm:0.14.0"
  dependencies:
    "@babel/core": "npm:^7.13.16"
    "@babel/parser": "npm:^7.13.16"
    "@babel/plugin-proposal-class-properties": "npm:^7.13.0"
    "@babel/plugin-proposal-nullish-coalescing-operator": "npm:^7.13.8"
    "@babel/plugin-proposal-optional-chaining": "npm:^7.13.12"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.13.8"
    "@babel/preset-flow": "npm:^7.13.13"
    "@babel/preset-typescript": "npm:^7.13.0"
    "@babel/register": "npm:^7.13.16"
    babel-core: "npm:^7.0.0-bridge.0"
    chalk: "npm:^4.1.2"
    flow-parser: "npm:0.*"
    graceful-fs: "npm:^4.2.4"
    micromatch: "npm:^4.0.4"
    neo-async: "npm:^2.5.0"
    node-dir: "npm:^0.1.17"
    recast: "npm:^0.21.0"
    temp: "npm:^0.8.4"
    write-file-atomic: "npm:^2.3.0"
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  bin:
    jscodeshift: bin/jscodeshift.js
  checksum: 10c0/dab63bdb4b7e67d79634fcd3f5dc8b227146e9f68aa88700bc49c5a45b6339d05bd934a98aa53d29abd04f81237d010e7e037799471b2aab66ec7b9a7d752786
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/dbf59312e0ebf2b4405ef413ec2b25abb5f8f4d9bc5fb8d9f90381622ebca5f2af6a6aa9a8578f65903f9e33990a6dc798edd0ce5586894bf0e9e31803a1de88
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: 10c0/2f1287a7c833e397c9ddd361a78638e828fc523038bb3441fd4fc144cfd2c6cd4963ffb9e207e648cf7b692600f1e1e524e965c32df5152120910e4903a47dcb
  languageName: node
  linkType: hard

"json-schema-deref-sync@npm:^0.13.0":
  version: 0.13.0
  resolution: "json-schema-deref-sync@npm:0.13.0"
  dependencies:
    clone: "npm:^2.1.2"
    dag-map: "npm:~1.0.0"
    is-valid-path: "npm:^0.1.1"
    lodash: "npm:^4.17.13"
    md5: "npm:~2.2.0"
    memory-cache: "npm:~0.2.0"
    traverse: "npm:~0.6.6"
    valid-url: "npm:~1.0.9"
  checksum: 10c0/07cc73d85c9ee6f8236444290cfd22ee4199cd6ddc049e329e7ec22103770b34653f95ae87c367aa49ba6551f09e58b649cd588732b67e7a17b3bb9860ecd061
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json5@npm:^2.2.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jwt-decode@npm:^4.0.0":
  version: 4.0.0
  resolution: "jwt-decode@npm:4.0.0"
  checksum: 10c0/de75bbf89220746c388cf6a7b71e56080437b77d2edb29bae1c2155048b02c6b8c59a3e5e8d6ccdfd54f0b8bda25226e491a4f1b55ac5f8da04cfbadec4e546c
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"legacy-swc-helpers@npm:@swc/helpers@=0.4.14":
  version: 0.4.14
  resolution: "@swc/helpers@npm:0.4.14"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/a8bd2e291fca73aa35ff316fb1aa9fb9554856518c8bf64ab5a355fb587d79d04d67f95033012fcdc94f507d22484871d95dc72efdd9ff13cc5d0ac68dfba999
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"lighthouse-logger@npm:^1.0.0":
  version: 1.4.2
  resolution: "lighthouse-logger@npm:1.4.2"
  dependencies:
    debug: "npm:^2.6.9"
    marky: "npm:^1.2.2"
  checksum: 10c0/090431db34e9ce01b03b2a03b39e998807a7a86214f2e8da2ba9588c36841caf4474f96ef1b2deaf9fe58f2e00f9f51618e0b98edecc2d8c9dfc13185bf0adc8
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-darwin-arm64@npm:1.19.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-darwin-x64@npm:1.19.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.19.0"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-linux-arm64-gnu@npm:1.19.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-linux-arm64-musl@npm:1.19.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-linux-x64-gnu@npm:1.19.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-linux-x64-musl@npm:1.19.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.19.0":
  version: 1.19.0
  resolution: "lightningcss-win32-x64-msvc@npm:1.19.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:~1.19.0":
  version: 1.19.0
  resolution: "lightningcss@npm:1.19.0"
  dependencies:
    detect-libc: "npm:^1.0.3"
    lightningcss-darwin-arm64: "npm:1.19.0"
    lightningcss-darwin-x64: "npm:1.19.0"
    lightningcss-linux-arm-gnueabihf: "npm:1.19.0"
    lightningcss-linux-arm64-gnu: "npm:1.19.0"
    lightningcss-linux-arm64-musl: "npm:1.19.0"
    lightningcss-linux-x64-gnu: "npm:1.19.0"
    lightningcss-linux-x64-musl: "npm:1.19.0"
    lightningcss-win32-x64-msvc: "npm:1.19.0"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/734cb578709d945cf272578fe30c9dec9462dedb24cbfdb80fdf21dd58ca9a7a347e2b11ec80b16c49964c5c7b4180adc2c5db2c93d2360fe27ca707b961b60f
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: "npm:^3.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 10c0/3db394b7829a7fe2f4fbdd25d3c4689b85f003c318c5da4052c7e56eed697da8f1bce5294f685c69ff76e32cba7a33629d94396976f6d05fb7f4c755c5e2ae8b
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10c0/2caf0e4808f319d761d2939ee0642fa6867a4bbf2cfce43276698828380756b99d4c4fa226d881655e6ac298dd453fe12a5ec8ba49861777759494c534936985
  languageName: node
  linkType: hard

"lodash.debounce@npm:4.0.8, lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.get@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.get@npm:4.4.2"
  checksum: 10c0/48f40d471a1654397ed41685495acb31498d5ed696185ac8973daef424a749ca0c7871bf7b665d5c14f5cc479394479e0307e781f61d5573831769593411be6e
  languageName: node
  linkType: hard

"lodash.has@npm:^4.5.2":
  version: 4.5.2
  resolution: "lodash.has@npm:4.5.2"
  checksum: 10c0/3ffa9e549f321996a5fdf6204494c035ff550b2df703f936a448c553131bbb55492b4e7995bb13500648b50b268ed8afc974a7a0c0a43744d28d61cc95cb1ffe
  languageName: node
  linkType: hard

"lodash.isempty@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.isempty@npm:4.4.0"
  checksum: 10c0/6c7eaa0802398736809b9e8aed8b8ac1abca9be71788fd719ba9d7f5b4c23e8dc63b7f049df4131713dda30a2fdedc2f655268e9deb8cd5a985dfc934afca194
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10c0/dfdb2356db19631a4b445d5f37868a095e2402292d59539a987f134a8778c62a2810c2452d11ae9e6dcac71fc9de40a6fedcb20e2952a15b431ad8b29e50e28f
  languageName: node
  linkType: hard

"lodash.isnil@npm:^4.0.0":
  version: 4.0.0
  resolution: "lodash.isnil@npm:4.0.0"
  checksum: 10c0/1a410a62eb2e797f077d038c11cbf1ea18ab36f713982849f086f86e050234d69988c76fa18d00278c0947daec67e9ecbc666326b8a06b43e36d3ece813a8120
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.mergewith@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.mergewith@npm:4.6.2"
  checksum: 10c0/4adbed65ff96fd65b0b3861f6899f98304f90fd71e7f1eb36c1270e05d500ee7f5ec44c02ef979b5ddbf75c0a0b9b99c35f0ad58f4011934c4d4e99e5200b3b5
  languageName: node
  linkType: hard

"lodash.omit@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.omit@npm:4.5.0"
  checksum: 10c0/3808b9b6faae35177174b6ab327f1177e29c91f1e98dcbccf13a72a6767bba337306449d537a4e0d8a33d2673f10d39bc732e30c4b803274ea0c1168ea60e549
  languageName: node
  linkType: hard

"lodash.omitby@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.omitby@npm:4.6.0"
  checksum: 10c0/4608b1d8c4063b63349a3462852465fbe74781d737fbb26a0a7f00b0e65f6ccbc13fa490a38f9380103d93fc398e3873983038efadfafc67ccafbb25d9bc7bf4
  languageName: node
  linkType: hard

"lodash.pick@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.pick@npm:4.4.0"
  checksum: 10c0/a04c460b95d1aaa44e9513d1dacf72ea74d838da843e45831de9de64c303f13cdde1859702a6f4dcef417816898ffd47c6ae0614c957ac70245bed2809b8d2e2
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 10c0/14628013e9e7f65ac904fc82fd8ecb0e55a9c4c2416434b1dd9cf64ae70a8937f0b15376a39a68248530adc64887ed0fe2b75204b2c9ec3eea1cb2d66ddd125d
  languageName: node
  linkType: hard

"lodash.uniqueid@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.uniqueid@npm:4.0.1"
  checksum: 10c0/053a001534258200ecde16e847f67b5220b6018d8dca0bd7fe39329624068bc42cf09c8f8a618b2bf50e5c5a4a62350e2984da57fbb886ca4e9c3226a96fea46
  languageName: node
  linkType: hard

"lodash@npm:^4.17.10, lodash@npm:^4.17.13, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: "npm:^2.0.1"
  checksum: 10c0/574eb4205f54f0605021aa67ebb372c30ca64e8ddd439efeb8507af83c776dce789e83614e80059014d9e48dcc94c4b60cef2e85f0dc944eea27c799cec62353
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: 10c0/67f445a9ffa76db1989d0fa98586e5bc2fd5247260dafb8ad93d9f0ccd5896d53fb830b0e54dade5ad838b9de2006c826831a3c528913093af20dff8bd24aca6
  languageName: node
  linkType: hard

"logkitty@npm:^0.7.1":
  version: 0.7.1
  resolution: "logkitty@npm:0.7.1"
  dependencies:
    ansi-fragments: "npm:^0.2.1"
    dayjs: "npm:^1.8.15"
    yargs: "npm:^15.1.0"
  bin:
    logkitty: bin/logkitty.js
  checksum: 10c0/2067fad55c0856c0608c51ab75f8ffa5a858c5f847fefa8ec0e5fd3aa0b7d732010169d187283b23583a72aa6b80bbbec4fc6801a6c47c3fac0fbb294786002a
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lottie-react-native@npm:6.7.0":
  version: 6.7.0
  resolution: "lottie-react-native@npm:6.7.0"
  peerDependencies:
    "@dotlottie/react-player": ^1.6.1
    "@lottiefiles/react-lottie-player": ^3.5.3
    react: "*"
    react-native: ">=0.46"
    react-native-windows: ">=0.63.x"
  peerDependenciesMeta:
    "@dotlottie/react-player":
      optional: true
    "@lottiefiles/react-lottie-player":
      optional: true
    react-native-windows:
      optional: true
  checksum: 10c0/a1df65252eb349414550a405b4fbb09e567404c123e98668701fcc325b38157e3f31a84c0563b1a4426417080b2ce5b901b1032910a3db695db96ffaf41030d6
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0, make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: "npm:^4.0.1"
    semver: "npm:^5.6.0"
  checksum: 10c0/ada869944d866229819735bee5548944caef560d7a8536ecbc6536edca28c72add47cc4f6fc39c54fb25d06b58da1f8994cf7d9df7dadea047064749efc085d8
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10c0/b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"marky@npm:^1.2.2":
  version: 1.3.0
  resolution: "marky@npm:1.3.0"
  checksum: 10c0/6619cdb132fdc4f7cd3e2bed6eebf81a38e50ff4b426bbfb354db68731e4adfebf35ebfd7c8e5a6e846cbf9b872588c4f76db25782caee8c1529ec9d483bf98b
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"md5-file@npm:^3.2.3":
  version: 3.2.3
  resolution: "md5-file@npm:3.2.3"
  dependencies:
    buffer-alloc: "npm:^1.1.0"
  bin:
    md5-file: cli.js
  checksum: 10c0/41d2c27534119bea6e7c1b1489290b4a412c256d3f184068753a215fbeb0eeb5d739334e753f997de5d7d104db3118c6ec2f6e50b1ed23d70deacefd098ee560
  languageName: node
  linkType: hard

"md5@npm:^2.2.1":
  version: 2.3.0
  resolution: "md5@npm:2.3.0"
  dependencies:
    charenc: "npm:0.0.2"
    crypt: "npm:0.0.2"
    is-buffer: "npm:~1.1.6"
  checksum: 10c0/14a21d597d92e5b738255fbe7fe379905b8cb97e0a49d44a20b58526a646ec5518c337b817ce0094ca94d3e81a3313879c4c7b510d250c282d53afbbdede9110
  languageName: node
  linkType: hard

"md5@npm:~2.2.0":
  version: 2.2.1
  resolution: "md5@npm:2.2.1"
  dependencies:
    charenc: "npm:~0.0.1"
    crypt: "npm:~0.0.1"
    is-buffer: "npm:~1.1.1"
  checksum: 10c0/e9e7de197a100169f27b956af63ece22348b2d06d40162c8d380d13dcbb7a307c95956857d0cb5ed92059f6448bbdce2d54bc6b922f8e6a36284c303ecc1612d
  languageName: node
  linkType: hard

"md5hex@npm:^1.0.0":
  version: 1.0.0
  resolution: "md5hex@npm:1.0.0"
  checksum: 10c0/cad2569cdbc61c9de1ff2724c7344c695d868579bb21a1ab4cedf3ea5e91fa75d74a861da071ea1ee00a161511104985c30cb08d797bfd7d99f0f8fd14994728
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 10c0/67241f8708c1e665a061d2b042d2d243366e93e5bf1f917693007f6d55111588b952dcbfd3ea9c2d0969fb754aad81b30fdcfdcc24546495fc3b24336b28d4bd
  languageName: node
  linkType: hard

"memoize-one@npm:^5.0.0":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: 10c0/fd22dbe9a978a2b4f30d6a491fc02fb90792432ad0dab840dc96c1734d2bd7c9cdeb6a26130ec60507eb43230559523615873168bcbe8fafab221c30b11d54c1
  languageName: node
  linkType: hard

"memory-cache@npm:~0.2.0":
  version: 0.2.0
  resolution: "memory-cache@npm:0.2.0"
  checksum: 10c0/d4fe58865dfdc252db18ae152ab6c9d62868cfc42d5e7f6cf30732fcf27f5f1f8d7b179c3b6f26f31a28ab1cc5c3937215c60aa9e8ad7ea8ff35e79f69ef14da
  languageName: node
  linkType: hard

"merge-options@npm:^3.0.4":
  version: 3.0.4
  resolution: "merge-options@npm:3.0.4"
  dependencies:
    is-plain-obj: "npm:^2.1.0"
  checksum: 10c0/02b5891ceef09b0b497b5a0154c37a71784e68ed71b14748f6fd4258ffd3fe4ecd5cb0fd6f7cae3954fd11e7686c5cb64486daffa63c2793bbe8b614b61c7055
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"metro-babel-transformer@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-babel-transformer@npm:0.80.12"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    flow-enums-runtime: "npm:^0.0.6"
    hermes-parser: "npm:0.23.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/8f546217f6564908cda6d7ce0f1715c6a3ea11cb83bd8368f95b3670b9b8567ed2eccde214ee9d82b024239af739d118949415b4b0ccb79f48935cdcecb7ca5d
  languageName: node
  linkType: hard

"metro-cache-key@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-cache-key@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/cc55c66353aac361dad42e7e2dd7c21a967cab2c311c026b1d1fe0bd36f1ab95e60e090d1d0736dde35eeb306e715262bce96a7e3748e82697cdebffd845913f
  languageName: node
  linkType: hard

"metro-cache@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-cache@npm:0.80.12"
  dependencies:
    exponential-backoff: "npm:^3.1.1"
    flow-enums-runtime: "npm:^0.0.6"
    metro-core: "npm:0.80.12"
  checksum: 10c0/92028c15fef2ef2d3e59bd9d226974999727bf77c65951405f11f854cb47f1935eb6991834b89a1e04b337985133ccd3ec29d99d3bc64fc36f9b25b7b7c8128f
  languageName: node
  linkType: hard

"metro-config@npm:0.80.12, metro-config@npm:^0.80.3":
  version: 0.80.12
  resolution: "metro-config@npm:0.80.12"
  dependencies:
    connect: "npm:^3.6.5"
    cosmiconfig: "npm:^5.0.5"
    flow-enums-runtime: "npm:^0.0.6"
    jest-validate: "npm:^29.6.3"
    metro: "npm:0.80.12"
    metro-cache: "npm:0.80.12"
    metro-core: "npm:0.80.12"
    metro-runtime: "npm:0.80.12"
  checksum: 10c0/435abd35a29ea677aa659c56f309189fbeeddc9127bec6bba711f88ea6115d7d2333e57f81c90daad55a551f059d71cfe82d990b4d4b14bd3d38e5f6abaf1462
  languageName: node
  linkType: hard

"metro-core@npm:0.80.12, metro-core@npm:^0.80.3":
  version: 0.80.12
  resolution: "metro-core@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    lodash.throttle: "npm:^4.1.1"
    metro-resolver: "npm:0.80.12"
  checksum: 10c0/0e9fecf50d42b4a0be97ed7ca2159a0a5d6f43b6dd3713b7c49fc6df33a13ff06e31861ea2d01445d317a2589d60e4aaa58efadf65131b3ea55e3c851755025c
  languageName: node
  linkType: hard

"metro-file-map@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-file-map@npm:0.80.12"
  dependencies:
    anymatch: "npm:^3.0.3"
    debug: "npm:^2.2.0"
    fb-watchman: "npm:^2.0.0"
    flow-enums-runtime: "npm:^0.0.6"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.4"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.6.3"
    micromatch: "npm:^4.0.4"
    node-abort-controller: "npm:^3.1.1"
    nullthrows: "npm:^1.1.1"
    walker: "npm:^1.0.7"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/c3cdf68b4c3c5cea83e4e543fa8ea602e13c0d6a979bf2058ac2d90b3b1f3b190a76283a5c6dd9870134cd685e33c7c6a1751cd1942b0ba8b4783485baa34885
  languageName: node
  linkType: hard

"metro-minify-terser@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-minify-terser@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    terser: "npm:^5.15.0"
  checksum: 10c0/54b90ab123a33eff8b4d44260b5a504626085a8a06b49bc57b25feca6faf8b86601f406f30e3cf85a4258e75a9740d6b2d15dab203e22047291ba02cbe18145f
  languageName: node
  linkType: hard

"metro-resolver@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-resolver@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/694bad3b2f5518ee30d5d181f1fc1109fb318d77e114962542b0fc1d797d159e7f3d13f0afaf89cea682ccdca6afdc544b45bcb9f2fb5af4e0b7c0ff2e135f96
  languageName: node
  linkType: hard

"metro-runtime@npm:0.80.12, metro-runtime@npm:^0.80.3":
  version: 0.80.12
  resolution: "metro-runtime@npm:0.80.12"
  dependencies:
    "@babel/runtime": "npm:^7.25.0"
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/a7f69ba457edfe0195f8a94f7da68fb8dbd35e648b277b016e89c78ef3e682c0660c8a36109534b4525a9a1d8727a83ee9e30b6c8d14a0a23c2f26de31ab44b7
  languageName: node
  linkType: hard

"metro-source-map@npm:0.80.12, metro-source-map@npm:^0.80.3":
  version: 0.80.12
  resolution: "metro-source-map@npm:0.80.12"
  dependencies:
    "@babel/traverse": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-symbolicate: "npm:0.80.12"
    nullthrows: "npm:^1.1.1"
    ob1: "npm:0.80.12"
    source-map: "npm:^0.5.6"
    vlq: "npm:^1.0.0"
  checksum: 10c0/94239360f6a3e4d64ea8f4d0eddbe4fdd3a160c5c5f6bf4b28ed48c586cf8e37b175d521eb0bad62608bd0ce3262020aebbc1942cf607f34662ca60add9a7db5
  languageName: node
  linkType: hard

"metro-symbolicate@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-symbolicate@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-source-map: "npm:0.80.12"
    nullthrows: "npm:^1.1.1"
    source-map: "npm:^0.5.6"
    through2: "npm:^2.0.1"
    vlq: "npm:^1.0.0"
  bin:
    metro-symbolicate: src/index.js
  checksum: 10c0/cab33281653d93e8c65632f539145929f296e01f45adb2fd9701411949b63b94b17a1ce581fdfb97551bf34f0a8f454c2dd3b923235727e00446b898f365bda3
  languageName: node
  linkType: hard

"metro-transform-plugins@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-transform-plugins@npm:0.80.12"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.0"
    "@babel/template": "npm:^7.0.0"
    "@babel/traverse": "npm:^7.20.0"
    flow-enums-runtime: "npm:^0.0.6"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/631ce5dc3dc029994ae19a76eff81e7d115dc16281b7447c63f301c50034b6b4df1898a23c65066d5b3034bfae2c504c69083a6790118cae5adca0c40a191e42
  languageName: node
  linkType: hard

"metro-transform-worker@npm:0.80.12":
  version: 0.80.12
  resolution: "metro-transform-worker@npm:0.80.12"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.0"
    "@babel/parser": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    flow-enums-runtime: "npm:^0.0.6"
    metro: "npm:0.80.12"
    metro-babel-transformer: "npm:0.80.12"
    metro-cache: "npm:0.80.12"
    metro-cache-key: "npm:0.80.12"
    metro-minify-terser: "npm:0.80.12"
    metro-source-map: "npm:0.80.12"
    metro-transform-plugins: "npm:0.80.12"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/816ed9c45827d089fad29e9096e9f35769555e540c0ea36f15af332c92e0fb3ef9f2f4e0549b318d3b2b8524fb3d778b7453a6243e91c9574252f0972239e535
  languageName: node
  linkType: hard

"metro@npm:0.80.12, metro@npm:^0.80.3":
  version: 0.80.12
  resolution: "metro@npm:0.80.12"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.0"
    "@babel/parser": "npm:^7.20.0"
    "@babel/template": "npm:^7.0.0"
    "@babel/traverse": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    accepts: "npm:^1.3.7"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^2.0.0"
    connect: "npm:^3.6.5"
    debug: "npm:^2.2.0"
    denodeify: "npm:^1.2.1"
    error-stack-parser: "npm:^2.0.6"
    flow-enums-runtime: "npm:^0.0.6"
    graceful-fs: "npm:^4.2.4"
    hermes-parser: "npm:0.23.1"
    image-size: "npm:^1.0.2"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.6.3"
    jsc-safe-url: "npm:^0.2.2"
    lodash.throttle: "npm:^4.1.1"
    metro-babel-transformer: "npm:0.80.12"
    metro-cache: "npm:0.80.12"
    metro-cache-key: "npm:0.80.12"
    metro-config: "npm:0.80.12"
    metro-core: "npm:0.80.12"
    metro-file-map: "npm:0.80.12"
    metro-resolver: "npm:0.80.12"
    metro-runtime: "npm:0.80.12"
    metro-source-map: "npm:0.80.12"
    metro-symbolicate: "npm:0.80.12"
    metro-transform-plugins: "npm:0.80.12"
    metro-transform-worker: "npm:0.80.12"
    mime-types: "npm:^2.1.27"
    nullthrows: "npm:^1.1.1"
    serialize-error: "npm:^2.1.0"
    source-map: "npm:^0.5.6"
    strip-ansi: "npm:^6.0.0"
    throat: "npm:^5.0.0"
    ws: "npm:^7.5.10"
    yargs: "npm:^17.6.2"
  bin:
    metro: src/cli.js
  checksum: 10c0/48c9113f4e30314a874fd95e01d532d8264e0c1c110bc88be5bc397730de9f2a948008c3155cda12fd1bb10634e676d0d6cb088591ca87a4fc6d108e281716db
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: "npm:^3.0.2"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/3d6505b20f9fa804af5d8c596cb1c5e475b9b0cd05f652c5b56141cf941bd72adaeb7a436fda344235cef93a7f29b7472efc779fcdb83b478eab0867b95cdeff
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:^2.1.35, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:^2.4.1":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 10c0/ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.2, ms@npm:^2.1.1":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 10c0/103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"nanoid@npm:^3.1.23, nanoid@npm:^3.3.7":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/e3fb661aa083454f40500473bb69eedb85dc160e763150b9a2c567c7e9ff560ce028a9f833123b618a6ea742e311138b591910e795614a629029e86e180660f3
  languageName: node
  linkType: hard

"native-base@npm:^3.4.28":
  version: 3.4.28
  resolution: "native-base@npm:3.4.28"
  dependencies:
    "@react-aria/visually-hidden": "npm:^3.2.1"
    "@react-native-aria/button": "npm:^0.2.4"
    "@react-native-aria/checkbox": "npm:^0.2.3"
    "@react-native-aria/combobox": "npm:^0.2.4-alpha.0"
    "@react-native-aria/focus": "npm:^0.2.6"
    "@react-native-aria/interactions": "npm:^0.2.2"
    "@react-native-aria/listbox": "npm:^0.2.4-alpha.3"
    "@react-native-aria/overlays": "npm:^0.3.3"
    "@react-native-aria/radio": "npm:^0.2.4"
    "@react-native-aria/slider": "npm:^0.2.5-alpha.1"
    "@react-native-aria/tabs": "npm:^0.2.7"
    "@react-native-aria/utils": "npm:^0.2.8"
    "@react-stately/checkbox": "npm:3.0.3"
    "@react-stately/collections": "npm:3.3.0"
    "@react-stately/combobox": "npm:3.0.0-alpha.1"
    "@react-stately/radio": "npm:3.2.1"
    "@react-stately/slider": "npm:3.0.1"
    "@react-stately/tabs": "npm:3.0.0-alpha.1"
    "@react-stately/toggle": "npm:3.2.1"
    inline-style-prefixer: "npm:^6.0.1"
    lodash.clonedeep: "npm:^4.5.0"
    lodash.get: "npm:^4.4.2"
    lodash.has: "npm:^4.5.2"
    lodash.isempty: "npm:^4.4.0"
    lodash.isequal: "npm:^4.5.0"
    lodash.isnil: "npm:^4.0.0"
    lodash.merge: "npm:^4.6.2"
    lodash.mergewith: "npm:^4.6.2"
    lodash.omit: "npm:^4.5.0"
    lodash.omitby: "npm:^4.6.0"
    lodash.pick: "npm:^4.4.0"
    lodash.uniqueid: "npm:^4.0.1"
    stable-hash: "npm:^0.0.2"
    tinycolor2: "npm:^1.4.2"
    use-sync-external-store: "npm:^1.2.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-native": "*"
    react: "*"
    react-dom: "*"
    react-native: "*"
    react-native-safe-area-context: "*"
    react-native-svg: "*"
  checksum: 10c0/84cea22b74f319ba1c4350df8ab9e8529611e0df1f0756705c9dc253545fa06b261fa5957668b1a483a55595fcbbec6cb11162cc5fd213b37910247d0effab71
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"nested-error-stacks@npm:~2.0.1":
  version: 2.0.1
  resolution: "nested-error-stacks@npm:2.0.1"
  checksum: 10c0/125049632bc3ca2252e994ca07f27d795c0e6decc4077f0f4163348d30d7cb95409ceff6184284c95396aa5ea8ff5010673063db7674058b966b4f0228d4981c
  languageName: node
  linkType: hard

"netone-selfcare@workspace:.":
  version: 0.0.0-use.local
  resolution: "netone-selfcare@workspace:."
  dependencies:
    "@babel/core": "npm:^7.24.0"
    "@react-native-async-storage/async-storage": "npm:^1.23.1"
    "@react-native-community/netinfo": "npm:11.3.1"
    "@react-navigation/bottom-tabs": "npm:^6.5.9"
    "@react-navigation/native": "npm:^6.1.8"
    "@react-navigation/native-stack": "npm:^6.9.13"
    "@shopify/flash-list": "npm:1.6.4"
    axios: "npm:^1.4.0"
    base-64: "npm:^1.0.0"
    deprecated-react-native-prop-types: "npm:^4.0.0"
    expo: "npm:~51.0.39"
    expo-blur: "npm:~13.0.2"
    expo-build-properties: "npm:~0.12.3"
    expo-clipboard: "npm:~6.0.3"
    expo-constants: "npm:~16.0.2"
    expo-contacts: "npm:~13.0.4"
    expo-font: "npm:~12.0.7"
    expo-linear-gradient: "npm:~13.0.2"
    expo-splash-screen: "npm:~0.27.5"
    expo-status-bar: "npm:~1.12.1"
    jwt-decode: "npm:^4.0.0"
    lottie-react-native: "npm:6.7.0"
    native-base: "npm:^3.4.28"
    react: "npm:18.2.0"
    react-dom: "npm:18.2.0"
    react-native: "npm:0.74.5"
    react-native-actions-sheet: "npm:^0.9.0-alpha.21"
    react-native-animatable: "npm:^1.4.0"
    react-native-contacts: "npm:^7.0.7"
    react-native-device-info: "npm:^10.11.0"
    react-native-gesture-handler: "npm:~2.16.1"
    react-native-keyboard-aware-scroll-view: "npm:^0.9.5"
    react-native-material-ripple: "npm:^0.9.1"
    react-native-root-siblings: "npm:^4.1.1"
    react-native-root-toast: "npm:^3.4.1"
    react-native-safe-area-context: "npm:^4.10.5"
    react-native-screens: "npm:3.31.1"
    react-native-size-matters: "npm:^0.4.0"
    react-native-svg: "npm:^15.2.0"
    react-native-user-agent: "npm:^2.3.1"
    styled-components: "npm:^6.0.7"
    zustand: "npm:^4.5.7"
  languageName: unknown
  linkType: soft

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 10c0/95568c1b73e1d0d4069a3e3061a2102d854513d37bcfda73300015b7ba4868d3b27c198d1dbbd8ebdef4112fc2ed9e895d4a0f2e1cce0bd334f2a1346dc9205f
  languageName: node
  linkType: hard

"nocache@npm:^3.0.1":
  version: 3.0.4
  resolution: "nocache@npm:3.0.4"
  checksum: 10c0/66e5db1206bee44173358c2264ae9742259273e9719535077fe27807441bad58f0deeadf3cec2aa62d4f86ccb8a0e067c9a64b6329684ddc30a57e377ec458ee
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.1.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 10c0/f7ad0e7a8e33809d4f3a0d1d65036a711c39e9d23e0319d80ebe076b9a3b4432b4d6b86a7fab65521de3f6872ffed36fc35d1327487c48eb88c517803403eda3
  languageName: node
  linkType: hard

"node-dir@npm:^0.1.17":
  version: 0.1.17
  resolution: "node-dir@npm:0.1.17"
  dependencies:
    minimatch: "npm:^3.0.2"
  checksum: 10c0/16222e871708c405079ff8122d4a7e1d522c5b90fc8f12b3112140af871cfc70128c376e845dcd0044c625db0d2efebd2d852414599d240564db61d53402b4c1
  languageName: node
  linkType: hard

"node-fetch@npm:^2.2.0, node-fetch@npm:^2.6.0, node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.12, node-fetch@npm:^2.6.7":
  version: 2.6.12
  resolution: "node-fetch@npm:2.6.12"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/10372e4b5ee07acadc15e6b2bc6fd8940582eea7b9b2a331f4e3665fdcd968498c1656f79f2fa572080ebb37ea80e1474a6478b3b36057ef901b63f4be8fd899
  languageName: node
  linkType: hard

"node-forge@npm:^1, node-forge@npm:^1.2.1, node-forge@npm:^1.3.1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 10c0/e882819b251a4321f9fc1d67c85d1501d3004b4ee889af822fd07f64de3d1a8e272ff00b689570af0465d65d6bf5074df9c76e900e0aff23e60b847f2a46fbe8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.4.2
  resolution: "node-gyp@npm:11.4.2"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/0bfd3e96770ed70f07798d881dd37b4267708966d868a0e585986baac487d9cf5831285579fd629a83dc4e434f53e6416ce301097f2ee464cb74d377e4d8bdbe
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10c0/a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"node-stream-zip@npm:^1.9.1":
  version: 1.15.0
  resolution: "node-stream-zip@npm:1.15.0"
  checksum: 10c0/429fce95d7e90e846adbe096c61d2ea8d18defc155c0345d25d0f98dd6fc72aeb95039318484a4e0a01dc3814b6d0d1ae0fe91847a29669dff8676ec064078c9
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-package-arg@npm:^11.0.0":
  version: 11.0.3
  resolution: "npm-package-arg@npm:11.0.3"
  dependencies:
    hosted-git-info: "npm:^7.0.0"
    proc-log: "npm:^4.0.0"
    semver: "npm:^7.3.5"
    validate-npm-package-name: "npm:^5.0.0"
  checksum: 10c0/e18333485e05c3a8774f4b5701ef74f4799533e650b70a68ca8dd697666c9a8d46932cb765fc593edce299521033bd4025a40323d5240cea8a393c784c0c285a
  languageName: node
  linkType: hard

"npm-package-arg@npm:^7.0.0":
  version: 7.0.0
  resolution: "npm-package-arg@npm:7.0.0"
  dependencies:
    hosted-git-info: "npm:^3.0.2"
    osenv: "npm:^0.1.5"
    semver: "npm:^5.6.0"
    validate-npm-package-name: "npm:^3.0.0"
  checksum: 10c0/2117c3ee2a9449db98c7d2efe92590867fcf68ab143b94a6ff53dee5a0c3343eab8f08a9f73bd6c15acca32f7635ea8b9a97b770ae1631c896a35ca9372a98c8
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: "npm:^2.0.0"
  checksum: 10c0/95549a477886f48346568c97b08c4fda9cdbf7ce8a4fbc2213f36896d0d19249e32d68d7451bdcbca8041b5fba04a6b2c4a618beaf19849505c05b700740f1de
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10c0/56f34bd7c3dcb3bd23481a277fa22918120459d3e9d95ca72976c72e9cac33a97483f0b95fc420e2eb546b9fe6db398273aba9a938650cdb8c98ee8f159dcb30
  languageName: node
  linkType: hard

"ob1@npm:0.80.12":
  version: 0.80.12
  resolution: "ob1@npm:0.80.12"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/844948e27a1ea22e9681a3a756c08031e3485641ff5ee224195557c6fbd4d1596a3c825b7b7ecde557e55ba17c4d7acdb32004c460d3cabb8e1234237bc33fdb
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/c904f9e518b11941eb60279a3cbfaf1289bd0001f600a950255b1dede9fe3df8cd74f38483550b3bb9485165166acb5db500c3b4c4337aec2815c88c96fcc2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.1.0":
  version: 1.1.0
  resolution: "on-headers@npm:1.1.0"
  checksum: 10c0/2c3b6b0d68ec9adbd561dc2d61c9b14da8ac03d8a2f0fd9e97bdf0600c887d5d97f664ff3be6876cf40cda6e3c587d73a4745e10b426ac50c7664fc5a0dfc0a1
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: "npm:^1.0.0"
  checksum: 10c0/b4e44a8c34e70e02251bfb578a6e26d6de6eedbed106cd78211d2fd64d28b6281d54924696554e4e966559644243753ac5df73c87f283b0927533d3315696215
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:^6.2.0":
  version: 6.4.0
  resolution: "open@npm:6.4.0"
  dependencies:
    is-wsl: "npm:^1.1.0"
  checksum: 10c0/447115632b4f3939fa0d973c33e17f28538fd268fd8257fc49763f7de6e76d29d65585b15998bbd2137337cfb70a92084a0e1b183a466e53a4829f704f295823
  languageName: node
  linkType: hard

"open@npm:^7.0.3":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: "npm:^2.0.0"
    is-wsl: "npm:^2.1.1"
  checksum: 10c0/77573a6a68f7364f3a19a4c80492712720746b63680ee304555112605ead196afe91052bd3c3d165efdf4e9d04d255e87de0d0a77acec11ef47fd5261251813f
  languageName: node
  linkType: hard

"open@npm:^8.0.4, open@npm:^8.3.0":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"ora@npm:3.4.0, ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: "npm:^2.4.2"
    cli-cursor: "npm:^2.1.0"
    cli-spinners: "npm:^2.0.0"
    log-symbols: "npm:^2.2.0"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/04cb375f222c36a16a95e6c39c473644a99a42fc34d35c37507cb836ea0a71f4d831fcd53198a460869114b2730891d63cc1047304afe5ddb078974d468edfb1
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: "npm:^4.1.0"
    chalk: "npm:^4.1.0"
    cli-cursor: "npm:^3.1.0"
    cli-spinners: "npm:^2.5.0"
    is-interactive: "npm:^1.0.0"
    is-unicode-supported: "npm:^0.1.0"
    log-symbols: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/10ff14aace236d0e2f044193362b22edce4784add08b779eccc8f8ef97195cae1248db8ec1ec5f5ff076f91acbe573f5f42a98c19b78dba8c54eefff983cae85
  languageName: node
  linkType: hard

"os-homedir@npm:^1.0.0":
  version: 1.0.2
  resolution: "os-homedir@npm:1.0.2"
  checksum: 10c0/6be4aa67317ee247b8d46142e243fb4ef1d2d65d3067f54bfc5079257a2f4d4d76b2da78cba7af3cb3f56dbb2e4202e0c47f26171d11ca1ed4008d842c90363f
  languageName: node
  linkType: hard

"os-tmpdir@npm:^1.0.0":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10c0/f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"osenv@npm:^0.1.5":
  version: 0.1.5
  resolution: "osenv@npm:0.1.5"
  dependencies:
    os-homedir: "npm:^1.0.0"
    os-tmpdir: "npm:^1.0.0"
  checksum: 10c0/b33ed4b77e662f3ee2a04bf4b56cad2107ab069dee982feb9e39ad44feb9aa0cf1016b9ac6e05d0d84c91fa496798fe48dd05a33175d624e51668068b9805302
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: "npm:^2.0.0"
  checksum: 10c0/7b7f06f718f19e989ce6280ed4396fb3c34dabdee0df948376483032f9d5ec22fdf7077ec942143a75827bb85b11da72016497fc10dac1106c837ed593969ee8
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: "npm:^1.3.1"
    json-parse-better-errors: "npm:^1.0.1"
  checksum: 10c0/8d80790b772ccb1bcea4e09e2697555e519d83d04a77c2b4237389b813f82898943a93ffff7d0d2406203bdd0c30dcf95b1661e3a53f83d0e417f053957bef32
  languageName: node
  linkType: hard

"parse-png@npm:^2.1.0":
  version: 2.1.0
  resolution: "parse-png@npm:2.1.0"
  dependencies:
    pngjs: "npm:^3.3.0"
  checksum: 10c0/5157a8bbb976ae1ca990fc53c7014d42aac0967cb30e2daf36c3fef1876c8db0d551e695400c904f33c5c5add76a572c65b5044721d62417d8cc7abe4c4ffa41
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 10c0/17d6a5664bc0a11d48e2b2127d28a0e58822c6740bde30403f08013da599182289c56518bec89407e3f31d3c2b6b296a4220bc3f867f0911fee6952208b04167
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0, path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: 10c0/dd2044f029a8e58ac31d2bf34c34b93c3095c1481942960e84dd2faa95bbb71b9b762a106aead0646695330936414b31ca0bd862bf488a937ad17c8c5d73b32b
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.5, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^3.0.1":
  version: 3.0.1
  resolution: "picomatch@npm:3.0.1"
  checksum: 10c0/70ec738569f1864658378b7abdab8939d15dae0718c1df994eae3346fd33daf6a3c1ff4e0c1a0cd1e2c0319130985b63a2cff34d192f2f2acbb78aca76111736
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.3":
  version: 4.0.3
  resolution: "picomatch@npm:4.0.3"
  checksum: 10c0/9582c951e95eebee5434f59e426cddd228a7b97a0161a375aed4be244bd3fe8e3a31b846808ea14ef2c8a2527a6eeab7b3946a67d5979e81694654f939473ae2
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10c0/6f9d404b0d47a965437403c9b90eca8bb2536407f03de165940e62e72c8c8b75adda5516c6b9b23675a5877cc0bcac6bdfb0ef0e39414cd2476d5495da40e7cf
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1, pirates@npm:^4.0.6":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 10c0/00d5fa51f8dded94d7429700fb91a0c1ead00ae2c7fd27089f0c5b63e6eca36197fe46384631872690a66f390c5e27198e99006ab77ae472692ab9c2ca903f36
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: "npm:^3.0.0"
  checksum: 10c0/902a3d0c1f8ac43b1795fa1ba6ffeb37dfd53c91469e969790f6ed5e29ff2bdc50b63ba6115dc056d2efb4a040aa2446d512b3804bdafdf302f734fb3ec21847
  languageName: node
  linkType: hard

"plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.5.1"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/db19ba50faafc4103df8e79bcd6b08004a56db2a9dd30b3e5c8b0ef30398ef44344a674e594d012c8fc39e539a2b72cb58c60a76b4b4401cbbc7c8f6b028d93d
  languageName: node
  linkType: hard

"pngjs@npm:^3.3.0":
  version: 3.4.0
  resolution: "pngjs@npm:3.4.0"
  checksum: 10c0/88ee73e2ad3f736e0b2573722309eb80bd2aa28916f0862379b4fd0f904751b4f61bb6bd1ecd7d4242d331f2b5c28c13309dd4b7d89a9b78306e35122fdc5011
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:^8.4.31, postcss@npm:~8.4.32":
  version: 8.4.38
  resolution: "postcss@npm:8.4.38"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.2.0"
  checksum: 10c0/955407b8f70cf0c14acf35dab3615899a2a60a26718a63c848cf3c29f2467b0533991b985a2b994430d890bd7ec2b1963e36352b0774a19143b5f591540f7c06
  languageName: node
  linkType: hard

"pretty-bytes@npm:5.6.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 10c0/f69f494dcc1adda98dbe0e4a36d301e8be8ff99bfde7a637b2ee2820e7cb583b0fc0f3a63b0e3752c01501185a5cf38602c7be60da41bdf84ef5b70e89c370f3
  languageName: node
  linkType: hard

"pretty-format@npm:^24":
  version: 24.9.0
  resolution: "pretty-format@npm:24.9.0"
  dependencies:
    "@jest/types": "npm:^24.9.0"
    ansi-regex: "npm:^4.0.0"
    ansi-styles: "npm:^3.2.0"
    react-is: "npm:^16.8.4"
  checksum: 10c0/1e75c0ae55dab8953a5fe8025aab0a6d6090773561b672a7a00108f6cfb7dace198b27143392382dff913cb71f6fbc10ed23beaddf2117c380588a3b575825f0
  languageName: node
  linkType: hard

"pretty-format@npm:^26.5.2, pretty-format@npm:^26.6.2":
  version: 26.6.2
  resolution: "pretty-format@npm:26.6.2"
  dependencies:
    "@jest/types": "npm:^26.6.2"
    ansi-regex: "npm:^5.0.0"
    ansi-styles: "npm:^4.0.0"
    react-is: "npm:^17.0.1"
  checksum: 10c0/b5ddf0e949b874b699d313fe9407f0eb65e67d00823b2dd95335905a73457260af7612f3bff6b48611fcca9ffcff003359e4c9faba4200d6209da433a859aef3
  languageName: node
  linkType: hard

"pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/edc5ff89f51916f036c62ed433506b55446ff739358de77207e63e88a28ca2894caac6e73dcb68166a606e51c8087d32d400473e6a9fdd2dbe743f46c9c0276f
  languageName: node
  linkType: hard

"proc-log@npm:^4.0.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 10c0/17db4757c2a5c44c1e545170e6c70a26f7de58feb985091fb1763f5081cab3d01b181fb2dd240c9f4a4255a1d9227d163d5771b7e69c9e49a561692db865efb9
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"progress@npm:2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: 10c0/1697e07cb1068055dbe9fe858d242368ff5d2073639e652b75a7eb1f2a1a8d4afd404d719de23c7b48481a6aa0040686310e2dac2f53d776daa2176d3f96369c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: "npm:~2.0.3"
  checksum: 10c0/742e5c0cc646af1f0746963b8776299701ad561ce2c70b49365d62c8db8ea3681b0a1bf0d4e2fe07910bf72f02d39e51e8e73dc8d7503c3501206ac908be107f
  languageName: node
  linkType: hard

"promise@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: "npm:~2.0.6"
  checksum: 10c0/6fccae27a10bcce7442daf090279968086edd2e3f6cebe054b71816403e2526553edf510d13088a4d0f14d7dfa9b9dfb188cab72d6f942e186a4353b6a29c8bf
  languageName: node
  linkType: hard

"prompts@npm:^2.3.2, prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"prop-types@npm:*, prop-types@npm:15.8.1, prop-types@npm:^15.5.10, prop-types@npm:^15.6.2, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/ada5cdf1d813065bbc99aa2c393b8f6beee73b5de2890a8754c9f488d7323ffd2ca5f5a0943b48934e3fcbd97637d0337369c3c631aeb9614915db629f1c75c9
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"qrcode-terminal@npm:0.11.0":
  version: 0.11.0
  resolution: "qrcode-terminal@npm:0.11.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: 10c0/7561a649d21d7672d451ada5f2a2b393f586627cea75670c97141dc2b4b4145db547e1fddf512a3552e7fb54de530d513a736cd604c840adb908ed03c32312ad
  languageName: node
  linkType: hard

"query-string@npm:^7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10c0/a896c08e9e0d4f8ffd89a572d11f668c8d0f7df9c27c6f49b92ab31366d3ba0e9c331b9a620ee747893436cd1f2f821a6327e2bc9776bde2402ac6c270b801b2
  languageName: node
  linkType: hard

"querystring@npm:^0.2.1":
  version: 0.2.1
  resolution: "querystring@npm:0.2.1"
  checksum: 10c0/6841b32bec4f16ffe7f5b5e4373b47ad451f079cde3a7f45e63e550f0ecfd8f8189ef81fb50079413b3fc1c59b06146e4c98192cb74ed7981aca72090466cd94
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"rc@npm:~1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"react-devtools-core@npm:^5.0.0":
  version: 5.3.2
  resolution: "react-devtools-core@npm:5.3.2"
  dependencies:
    shell-quote: "npm:^1.6.1"
    ws: "npm:^7"
  checksum: 10c0/7165544ca5890af62e875eeda3f915e054dc734ad74f77d6490de32ba4fef6c1d30647bbb0643f769dd988913e0edc2bf2b1d6c2679e910150929a6312479cf3
  languageName: node
  linkType: hard

"react-dom@npm:18.2.0":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.0"
  peerDependencies:
    react: ^18.2.0
  checksum: 10c0/66dfc5f93e13d0674e78ef41f92ed21dfb80f9c4ac4ac25a4b51046d41d4d2186abc915b897f69d3d0ebbffe6184e7c5876f2af26bfa956f179225d921be713a
  languageName: node
  linkType: hard

"react-freeze@npm:^1.0.0":
  version: 1.0.3
  resolution: "react-freeze@npm:1.0.3"
  peerDependencies:
    react: ">=17.0.0"
  checksum: 10c0/d42cb9c0efbb4a9c38d3c9a5ee060febb273bf0acee2ccacba8d70abda1a47dfe678979bc7d421d9674fa9cb23085e95eeaae7b24dac29ab059f60c29c3eee71
  languageName: node
  linkType: hard

"react-is@npm:^16.12.0 || ^17.0.0 || ^18.0.0, react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 10c0/2bdb6b93fbb1820b024b496042cce405c57e2f85e777c9aabd55f9b26d145408f9f74f5934676ffdc46f3dcff656d78413a6e43968e7b3f92eea35b3052e9053
  languageName: node
  linkType: hard

"react-is@npm:^16.13.0, react-is@npm:^16.13.1, react-is@npm:^16.7.0, react-is@npm:^16.8.4":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"react-native-actions-sheet@npm:^0.9.0-alpha.21":
  version: 0.9.0-alpha.24
  resolution: "react-native-actions-sheet@npm:0.9.0-alpha.24"
  peerDependencies:
    react-native: "*"
    react-native-gesture-handler: "*"
  checksum: 10c0/ff4846aad93461d0c83d7422624044e5f816ae06ef863e2d1ff2e40127e4d68588cee0a21031648df52134a9d84710868f3f87a14c70ea47ba5c410bc1fa4333
  languageName: node
  linkType: hard

"react-native-animatable@npm:^1.4.0":
  version: 1.4.0
  resolution: "react-native-animatable@npm:1.4.0"
  dependencies:
    prop-types: "npm:^15.8.1"
  checksum: 10c0/658f15aa3744c3d6a22a44ab4609afc4335ff06d0a200a1fba513e470ebb4bd561ab709204b3777ee53cad526f695e2b4290e057a50fcd461aca337e407fe2b1
  languageName: node
  linkType: hard

"react-native-contacts@npm:^7.0.7":
  version: 7.0.7
  resolution: "react-native-contacts@npm:7.0.7"
  peerDependencies:
    react-native: ">=0.64.0"
  checksum: 10c0/fecd0b37fbc320dc25cf9be852a3dff6d6db4380dbed0225e734fc3532adcdc66e508c790075fa258759a07f4a63fb561c3724b98d49040eea1eac2a5c8cf43b
  languageName: node
  linkType: hard

"react-native-device-info@npm:^10.11.0":
  version: 10.11.0
  resolution: "react-native-device-info@npm:10.11.0"
  peerDependencies:
    react-native: "*"
  checksum: 10c0/743650793e0d1f1b79a4e45308299aaf14953067de512522497455931afd42c7a334c248eb918403cff9129461fb4d988c7c2ec1641d26122867d7f51ce45d7d
  languageName: node
  linkType: hard

"react-native-gesture-handler@npm:~2.16.1":
  version: 2.16.2
  resolution: "react-native-gesture-handler@npm:2.16.2"
  dependencies:
    "@egjs/hammerjs": "npm:^2.0.17"
    hoist-non-react-statics: "npm:^3.3.0"
    invariant: "npm:^2.2.4"
    lodash: "npm:^4.17.21"
    prop-types: "npm:^15.7.2"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/cd31794870edc51bc7051bf22893b19cebda5754c2fbd381f72e6ae6a718c26c5cf6966ad2f80e15e3a3217d09cc3e2e1637132f70bf44aacf62ea15a7636c98
  languageName: node
  linkType: hard

"react-native-iphone-x-helper@npm:^1.0.3":
  version: 1.3.1
  resolution: "react-native-iphone-x-helper@npm:1.3.1"
  peerDependencies:
    react-native: ">=0.42.0"
  checksum: 10c0/f2e01cb30ebdba54cbadc7bcb584ef7491c24aa2d221312f084c6e7b1031b77c2d538f7bf778ee16959245dbbe4f7784bbead02448368b7d0cbe8dd8c808693f
  languageName: node
  linkType: hard

"react-native-keyboard-aware-scroll-view@npm:^0.9.5":
  version: 0.9.5
  resolution: "react-native-keyboard-aware-scroll-view@npm:0.9.5"
  dependencies:
    prop-types: "npm:^15.6.2"
    react-native-iphone-x-helper: "npm:^1.0.3"
  peerDependencies:
    react-native: ">=0.48.4"
  checksum: 10c0/932c724f86ba355f1a1db938123eef67844550dd6e2d117222eff0012a47c6a293ad79c965e16930e5eb14adb58a5c340c00e03eeabbe3d78229236125b8a6aa
  languageName: node
  linkType: hard

"react-native-material-ripple@npm:^0.9.1":
  version: 0.9.1
  resolution: "react-native-material-ripple@npm:0.9.1"
  dependencies:
    prop-types: "npm:^15.5.10"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/36970efaabd03e76dbaf419624ee132237b5019f5a4f6a4260c05d9c92ff4ca53e3006fa3ea50267d2e7d7a0a860aa89aa05dbfd6c139800b6fa2453c15408d9
  languageName: node
  linkType: hard

"react-native-root-siblings@npm:^4.0.0, react-native-root-siblings@npm:^4.1.1":
  version: 4.1.1
  resolution: "react-native-root-siblings@npm:4.1.1"
  checksum: 10c0/48b86775d44d926db7ccc34c5beb4b4140904db008afde103d0f758289fffa769010113e31be297fa7aa56552812272cecdf379cf9e3bf9430dfaace398dfe8c
  languageName: node
  linkType: hard

"react-native-root-toast@npm:^3.4.1":
  version: 3.5.1
  resolution: "react-native-root-toast@npm:3.5.1"
  dependencies:
    deprecated-react-native-prop-types: "npm:^2.3.0"
    prop-types: "npm:^15.5.10"
    react-native-root-siblings: "npm:^4.0.0"
  peerDependencies:
    react-native: ">=0.47.0"
  checksum: 10c0/295fb55d1c79bc7c46b8f43d3b2cb5bf59d1d20e9726648f40bb1f1a50c081085e15c3a58f9193d447a346d5e4ff2c19970e1c9ade5a709b200c8dc629f07482
  languageName: node
  linkType: hard

"react-native-safe-area-context@npm:^4.10.5":
  version: 4.10.5
  resolution: "react-native-safe-area-context@npm:4.10.5"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/b385d07ad0bd8400f957e458cf9f715312ccfe00db0c0806831a419f44fbe1eef5004c7d007156db8bc235f738880d2467d89e334ab585c682d38c0c8635c7f1
  languageName: node
  linkType: hard

"react-native-screens@npm:3.31.1":
  version: 3.31.1
  resolution: "react-native-screens@npm:3.31.1"
  dependencies:
    react-freeze: "npm:^1.0.0"
    warn-once: "npm:^0.1.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/899a02751ecbc6231ce3c869268fb510e5b53428c788135ae46f4bbd768efe78bf74f17fec5becc4c4c1e81a5f224a907dc890ba2406181ff9e21830dda95dec
  languageName: node
  linkType: hard

"react-native-size-matters@npm:^0.4.0":
  version: 0.4.2
  resolution: "react-native-size-matters@npm:0.4.2"
  peerDependencies:
    react-native: "*"
  checksum: 10c0/56715d33d02f10d4ca358761420cc9a971b3872c11b9fa403afd4f0b2bbf74148fd3c4c799959727fb61c576a474e3e1155ffadc247c12d70fadcb562b4a9f29
  languageName: node
  linkType: hard

"react-native-svg@npm:^15.2.0":
  version: 15.2.0
  resolution: "react-native-svg@npm:15.2.0"
  dependencies:
    css-select: "npm:^5.1.0"
    css-tree: "npm:^1.1.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/9b979dbf101ee3131c606c7960f1bc8c49b3f0acb42b394e9b89f33b853e20765d93844178ad545747e73b2ffd5654d6e486daf86987865983913c46d31bb2c8
  languageName: node
  linkType: hard

"react-native-user-agent@npm:^2.3.1":
  version: 2.3.1
  resolution: "react-native-user-agent@npm:2.3.1"
  peerDependencies:
    react-native: "*"
  checksum: 10c0/8bfe6874f272717fbc5bea0b978597129c6e1236cda375d5a005b689a123d403c4ec08e65ba4cc47a718a05ea7f7972bcc275e64b77255edcef6f01fa3e1f4b5
  languageName: node
  linkType: hard

"react-native@npm:0.74.5":
  version: 0.74.5
  resolution: "react-native@npm:0.74.5"
  dependencies:
    "@jest/create-cache-key-function": "npm:^29.6.3"
    "@react-native-community/cli": "npm:13.6.9"
    "@react-native-community/cli-platform-android": "npm:13.6.9"
    "@react-native-community/cli-platform-ios": "npm:13.6.9"
    "@react-native/assets-registry": "npm:0.74.87"
    "@react-native/codegen": "npm:0.74.87"
    "@react-native/community-cli-plugin": "npm:0.74.87"
    "@react-native/gradle-plugin": "npm:0.74.87"
    "@react-native/js-polyfills": "npm:0.74.87"
    "@react-native/normalize-colors": "npm:0.74.87"
    "@react-native/virtualized-lists": "npm:0.74.87"
    abort-controller: "npm:^3.0.0"
    anser: "npm:^1.4.9"
    ansi-regex: "npm:^5.0.0"
    base64-js: "npm:^1.5.1"
    chalk: "npm:^4.0.0"
    event-target-shim: "npm:^5.0.1"
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    jest-environment-node: "npm:^29.6.3"
    jsc-android: "npm:^250231.0.0"
    memoize-one: "npm:^5.0.0"
    metro-runtime: "npm:^0.80.3"
    metro-source-map: "npm:^0.80.3"
    mkdirp: "npm:^0.5.1"
    nullthrows: "npm:^1.1.1"
    pretty-format: "npm:^26.5.2"
    promise: "npm:^8.3.0"
    react-devtools-core: "npm:^5.0.0"
    react-refresh: "npm:^0.14.0"
    react-shallow-renderer: "npm:^16.15.0"
    regenerator-runtime: "npm:^0.13.2"
    scheduler: "npm:0.24.0-canary-efb381bbf-20230505"
    stacktrace-parser: "npm:^0.1.10"
    whatwg-fetch: "npm:^3.0.0"
    ws: "npm:^6.2.2"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@types/react": ^18.2.6
    react: 18.2.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  bin:
    react-native: cli.js
  checksum: 10c0/5b1ad86b64e42a209a2feea5cdfae14147affc351a672cbc9a5beaf9b522fb50b26c1ffe8dbe3ea5343307879a0691e2f49a73265ec18ba165539f3c23f407a0
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.0, react-refresh@npm:^0.14.2":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: 10c0/875b72ef56b147a131e33f2abd6ec059d1989854b3ff438898e4f9310bfcc73acff709445b7ba843318a953cb9424bcc2c05af2b3d80011cee28f25aef3e2ebb
  languageName: node
  linkType: hard

"react-shallow-renderer@npm:^16.15.0":
  version: 16.15.0
  resolution: "react-shallow-renderer@npm:16.15.0"
  dependencies:
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.12.0 || ^17.0.0 || ^18.0.0"
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: 10c0/c194d741792e86043a4ae272f7353c1cb9412bc649945c4220c6a101a6ea5410cceb3d65d5a4d750f11a24f7426e8eec7977e8a4e3ad5d3ee235ca2b18166fa8
  languageName: node
  linkType: hard

"react@npm:18.2.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/b562d9b569b0cb315e44b48099f7712283d93df36b19a39a67c254c6686479d3980b7f013dc931f4a5a3ae7645eae6386b4aa5eea933baa54ecd0f9acb0902b8
  languageName: node
  linkType: hard

"readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readline@npm:^1.3.0":
  version: 1.3.0
  resolution: "readline@npm:1.3.0"
  checksum: 10c0/7404c9edc3fd29430221ef1830867c8d87e50612e4ce70f84ecd55686f7db1c81d67c6a4dcb407839f4c459ad05dd34524a2c7a97681e91878367c68d0e38665
  languageName: node
  linkType: hard

"recast@npm:^0.21.0":
  version: 0.21.5
  resolution: "recast@npm:0.21.5"
  dependencies:
    ast-types: "npm:0.15.2"
    esprima: "npm:~4.0.0"
    source-map: "npm:~0.6.1"
    tslib: "npm:^2.0.1"
  checksum: 10c0/a45168c82195f24fa2c70293a624fece0069a2e8e8adb637f9963777735f81cb3bb62e55172db677ec3573b08b2daaf1eddd85b74da6fe0bd37c9b15eeaf94b4
  languageName: node
  linkType: hard

"recyclerlistview@npm:4.2.0":
  version: 4.2.0
  resolution: "recyclerlistview@npm:4.2.0"
  dependencies:
    lodash.debounce: "npm:4.0.8"
    prop-types: "npm:15.8.1"
    ts-object-utils: "npm:0.0.5"
  peerDependencies:
    react: ">= 15.2.1"
    react-native: ">= 0.30.0"
  checksum: 10c0/eee8e128f90d1c7fe97bc3c1204fd3b959e5895bef76bb45510f0eb23b2b72f5f7c48a87819abdefd0b8eb1dc7f76af57e3dcbaabcfb136306e6e8dee7c0375b
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.2":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"remove-trailing-slash@npm:^0.1.0":
  version: 0.1.1
  resolution: "remove-trailing-slash@npm:0.1.1"
  checksum: 10c0/6fa91e7b89e0675fdca6ce54af5fad9bd612d51e2251913a2e113b521b157647f1f8c694b55447780b489b30a63ebe949ccda7411ef383d09136bb27121c6c09
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10c0/db91467d9ead311b4111cbd73a4e67fa7820daed2989a32f7023785a2659008c6d119752d9c4ac011ae07e537eb86523adff99804c5fdb39cd3a017f9b401bb6
  languageName: node
  linkType: hard

"requireg@npm:^0.2.2":
  version: 0.2.2
  resolution: "requireg@npm:0.2.2"
  dependencies:
    nested-error-stacks: "npm:~2.0.1"
    rc: "npm:~1.2.7"
    resolve: "npm:~1.7.1"
  checksum: 10c0/806cff08d8fa63f2ec9c74fa9602c86b56627a824d0a188bf777c8d82ba012a1b3c01ab6e88ffcf610713b6bc5ec8a9f9e55dc941b7606ce735e72c4d9daa059
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: 10c0/24affcf8e81f4c62f0dcabc774afe0e19c1f38e34e43daac0ddb409d79435fc3037f612b0cc129178b8c220442c3babd673e88e870d27215c99454566e770ebc
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-workspace-root@npm:2.0.0"
  checksum: 10c0/658e6fbc199c51f4903867ab371f03122d9865b4fb4fd3a2069c39b429132d91535e5112f5c6c561fa0852cb8393505b7f94b58c3e2566bab610a48172f38e3f
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.2":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10c0/1ade1493f4642a6267d0a5e68faeac20b3d220f18c28b140343feb83694d8fed7a286852aef43689d16042c61e2ddb270be6578ad4a13990769e12065191200d
  languageName: node
  linkType: hard

"resolve@npm:^1.22.10, resolve@npm:^1.22.2":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@npm:~1.7.1":
  version: 1.7.1
  resolution: "resolve@npm:1.7.1"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/6e9e29185ac57801aff013849e9717c769ef0a27eac30b6492405ba3d61db73d8967023b96578f4b2deba4ef5fb11fc4f0a4db47c0f536890ced5c014e94fbde
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.22.10#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.2#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A~1.7.1#optional!builtin<compat/resolve>":
  version: 1.7.1
  resolution: "resolve@patch:resolve@npm%3A1.7.1#optional!builtin<compat/resolve>::version=1.7.1&hash=3bafbf"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/1301dba7c12cd9dab2ab4eee8518089f25bb7480db34b746a923ded472c4c0600ebb1ba9b8028ca843f7c6017ac76524355800c52b82633e53bd601ca288b4de
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: "npm:^2.0.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/f5b335bee06f440445e976a7031a3ef53691f9b7c4a9d42a469a0edaf8a5508158a0d561ff2b26a1f4f38783bcca2c0e5c3a44f927326f6694d5b44d7a4993e6
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10c0/c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:~2.6.2":
  version: 2.6.3
  resolution: "rimraf@npm:2.6.3"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/f1e646f8c567795f2916aef7aadf685b543da6b9a53e482bb04b07472c7eef2b476045ba1e29f401c301c66b630b22b815ab31fdd60c5e1ae6566ff523debf45
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.2.4
  resolution: "sax@npm:1.2.4"
  checksum: 10c0/6e9b05ff443ee5e5096ce92d31c0740a20d33002fad714ebcb8fc7a664d9ee159103ebe8f7aef0a1f7c5ecacdd01f177f510dff95611c589399baf76437d3fe3
  languageName: node
  linkType: hard

"scheduler@npm:0.24.0-canary-efb381bbf-20230505":
  version: 0.24.0-canary-efb381bbf-20230505
  resolution: "scheduler@npm:0.24.0-canary-efb381bbf-20230505"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/4fb594d64c692199117160bbd1a5261f03287f8ec59d9ca079a772e5fbb3139495ebda843324d7c8957c07390a0825acb6f72bd29827fb9e155d793db6c2e2bc
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/26383305e249651d4c58e6705d5f8425f153211aef95f15161c151f7b8de885f24751b377e4a0b3dd42cce09aad3f87a61dab7636859c0d89b7daf1a1e2a5c78
  languageName: node
  linkType: hard

"selfsigned@npm:^2.4.1":
  version: 2.4.1
  resolution: "selfsigned@npm:2.4.1"
  dependencies:
    "@types/node-forge": "npm:^1.3.0"
    node-forge: "npm:^1"
  checksum: 10c0/521829ec36ea042f7e9963bf1da2ed040a815cf774422544b112ec53b7edc0bc50a0f8cc2ae7aa6cc19afa967c641fd96a15de0fc650c68651e41277d2e1df09
  languageName: node
  linkType: hard

"semver@npm:^5.5.0, semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10c0/e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.2, semver@npm:^7.5.4, semver@npm:^7.6.0":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"send@npm:^0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/0eb134d6a51fc13bbcb976a1f4214ea1e33f242fae046efc311e80aff66c7a43603e26a79d9d06670283a13000e51be6e0a2cb80ff0942eaf9f1cd30b7ae736a
  languageName: node
  linkType: hard

"serialize-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "serialize-error@npm:2.1.0"
  checksum: 10c0/919c40d293cd36b16bb3fce38a3a460e0c51a34cf0ee59815bbeec7c48ffe0a66ea2dec08aa5340ef6dfc1f22e7317f6e1ed76cdbb2ec3c494c0c4debfb344f8
  languageName: node
  linkType: hard

"serve-static@npm:^1.13.1":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10c0/7bab09613a1b9f480c85a9823aebec533015579fa055ba6634aa56ba1f984380670eaf33b8217502931872aa1401c9fcadaa15f9f604d631536df475b05bcf1e
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10c0/b926efb51cd0f47aa9bc061add788a4a650550bbe50647962113a4579b60af2abe7b62f9b02314acc6f97151d4cf87033a2b15fc20852fae306d1a095215396c
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: "npm:^1.0.0"
  checksum: 10c0/7b20dbf04112c456b7fc258622dafd566553184ac9b6938dd30b943b065b21dabd3776460df534cc02480db5e1b6aec44700d985153a3da46e7db7f9bd21326d
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 10c0/9abc45dee35f554ae9453098a13fdc2f1730e525a5eb33c51f096cc31f6f10a4b38074c1ebf354ae7bffa7229506083844008dfc3bb7818228568c0b2dc1fff2
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1, shell-quote@npm:^1.7.3":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 10c0/bee87c34e1e986cfb4c30846b8e6327d18874f10b535699866f368ade11ea4ee45433d97bf5eada22c4320c27df79c3a6a7eb1bf3ecfc47f2c997d9e5e2672fd
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.3.1
  resolution: "simple-plist@npm:1.3.1"
  dependencies:
    bplist-creator: "npm:0.1.0"
    bplist-parser: "npm:0.3.1"
    plist: "npm:^3.0.5"
  checksum: 10c0/3d5adeb705815338b1f4615c52584d540b12575337a0e0688f0a2b19a6a4162769cd8a3a36e9eb2b0fc9e27d63dcba8b9088a13e93eabcb7cdec5fe90ec5b0a5
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slice-ansi@npm:^2.0.0":
  version: 2.1.0
  resolution: "slice-ansi@npm:2.1.0"
  dependencies:
    ansi-styles: "npm:^3.2.0"
    astral-regex: "npm:^1.0.0"
    is-fullwidth-code-point: "npm:^2.0.0"
  checksum: 10c0/c317b21ec9e3d3968f3d5b548cbfc2eae331f58a03f1352621020799cbe695b3611ee972726f8f32d4ca530065a5ec9c74c97fde711c1f41b4a1585876b2c191
  languageName: node
  linkType: hard

"slugify@npm:^1.3.4, slugify@npm:^1.6.6":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 10c0/e7e63f08f389a371d6228bc19d64ec84360bf0a538333446cc49dbbf3971751a6d180d2f31551188dd007a65ca771e69f574e0283290a7825a818e90b75ef44d
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.7
  resolution: "socks@npm:2.8.7"
  dependencies:
    ip-address: "npm:^10.0.1"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/2805a43a1c4bcf9ebf6e018268d87b32b32b06fbbc1f9282573583acc155860dc361500f89c73bfbb157caa1b4ac78059eac0ef15d1811eb0ca75e0bdadbc9d2
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.0":
  version: 1.2.0
  resolution: "source-map-js@npm:1.2.0"
  checksum: 10c0/7e5f896ac10a3a50fe2898e5009c58ff0dc102dcb056ed27a354623a0ece8954d4b2649e1a1b2b52ef2e161d26f8859c7710350930751640e71e374fe2d321a4
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.16, source-map-support@npm:~0.5.20, source-map-support@npm:~0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:^0.5.0, source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"source-map@npm:^0.7.3":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10c0/56df8344f5a5de8521898a5c090023df1d8b8c75be6228f56c52491e0fc1617a5236f2ac3a066adb67a73231eac216ccea7b5b4a2423a543c277cb2f48d24c29
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.6
  resolution: "ssri@npm:10.0.6"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/e5a1e23a4057a86a97971465418f22ea89bd439ac36ade88812dd920e4e61873e8abd6a9b72a03a67ef50faa00a2daf1ab745c5a15b46d03e0544a0296354227
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.2":
  version: 0.0.2
  resolution: "stable-hash@npm:0.0.2"
  checksum: 10c0/7e59a8883353eaba94269fb5c23ad9627552cbc51ed8aa1bf302be607786f152cfe9b73079e00a44d0543d028effc015136dc86c25e6122f9caf83bddf84816d
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10c0/651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 10c0/18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.10
  resolution: "stacktrace-parser@npm:0.1.10"
  dependencies:
    type-fest: "npm:^0.7.1"
  checksum: 10c0/f9c9cd55b0642a546e5f0516a87124fc496dcc2c082b96b156ed094c51e423314795cd1839cd4c59026349cf392d3414f54fc42165255602728588a58a9f72d3
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10c0/de4e45706bb4c0354a4b1122a2b8cc45a639e86206807ce0baf390ee9218d3ef181923fa4d2b67443367c491aa255c5fbaa64bb74648e3c5b48299928af86c09
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x, stream-buffers@npm:~2.2.0":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 10c0/14a351f0a066eaa08c8c64a74f4aedd87dd7a8e59d4be224703da33dca3eb370828ee6c0ae3fff59a9c743e8098728fc95c5f052ae7741672a31e6b1430ba50a
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10c0/010cbc78da0e2cf833b0f5dc769e21ae74cdc5d5f5bd555f14a4a4876c8ad2c85ab8b5bdf9a722dc71a11dcd3184085e1c3c0bd50ec6bb85fffc0f28cf82597d
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.0.0, strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: "npm:^4.1.0"
  checksum: 10c0/de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 10c0/f336beed8622f7c1dd02f2cbd8422da9208fae81daf184f73656332899978919d5c0ca84dc6cfc49ad1fc4dd7badcde5412a063cf4e0d7f8ed95a13a63f68f45
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"strnum@npm:^1.1.1":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: 10c0/a0fce2498fa3c64ce64a40dada41beb91cabe3caefa910e467dc0518ef2ebd7e4d10f8c2202a6104f1410254cae245066c0e94e2521fb4061a5cb41831952392
  languageName: node
  linkType: hard

"structured-headers@npm:^0.4.1":
  version: 0.4.1
  resolution: "structured-headers@npm:0.4.1"
  checksum: 10c0/b7d326f6fec7e7f7901d1e0542577293b5d029bf3e1fb84995e33d9aabe47d03259f64ca2d778ef5c427f6f00c78bafa051b6f233131e1556f8bb9102b11ed64
  languageName: node
  linkType: hard

"styled-components@npm:^6.0.7":
  version: 6.1.0
  resolution: "styled-components@npm:6.1.0"
  dependencies:
    "@emotion/is-prop-valid": "npm:^1.2.1"
    "@emotion/unitless": "npm:^0.8.0"
    "@types/stylis": "npm:^4.0.2"
    css-to-react-native: "npm:^3.2.0"
    csstype: "npm:^3.1.2"
    postcss: "npm:^8.4.31"
    shallowequal: "npm:^1.1.0"
    stylis: "npm:^4.3.0"
    tslib: "npm:^2.5.0"
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
  checksum: 10c0/7270e592f152dbc0a7275adb5c1c951305a4eb88305c13dc11b65a12f9a23cdc043dde4acd542174f3831f122649ac9c6a31b1da088a2586c29e7382cd0fd923
  languageName: node
  linkType: hard

"stylis@npm:^4.3.0":
  version: 4.3.0
  resolution: "stylis@npm:4.3.0"
  checksum: 10c0/5a9f7e0cf2a15591efaacc1c6416a8785d2b57522cd38bb8e0a81a03c23d3bea2363659fa5f9d486a73d1b6ebaf1d32826ce1c1974c95afdb5b495d98acb25c0
  languageName: node
  linkType: hard

"sucrase@npm:3.34.0":
  version: 3.34.0
  resolution: "sucrase@npm:3.34.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    commander: "npm:^4.0.0"
    glob: "npm:7.1.6"
    lines-and-columns: "npm:^1.1.6"
    mz: "npm:^2.7.0"
    pirates: "npm:^4.0.1"
    ts-interface-checker: "npm:^0.1.9"
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 10c0/83e524f2b9386c7029fc9e46b8d608485866d08bea5a0a71e9e3442dc12e1d05a5ab555808d1922f45dd012fc71043479d778aac07391d9740daabe45730a056
  languageName: node
  linkType: hard

"sudo-prompt@npm:^9.0.0":
  version: 9.2.1
  resolution: "sudo-prompt@npm:9.2.1"
  checksum: 10c0/e56793513a9c95f66367a3be2ec4c1adee84a2a62f1b7ff6453d610586dcd373d7d8f4df522a7dae03aea8b779ef7f7ba25d1130d24fb1e495cfbbc2c72c7486
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"tar@npm:^6.0.5, tar@npm:^6.1.11":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"temp-dir@npm:^1.0.0":
  version: 1.0.0
  resolution: "temp-dir@npm:1.0.0"
  checksum: 10c0/648669d5e154d1961217784c786acadccf0156519c19e0aceda7edc76f5bdfa32a40dd7f88ebea9238ed6e3dedf08b846161916c8947058c384761351be90a8e
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: 10c0/b1df969e3f3f7903f3426861887ed76ba3b495f63f6d0c8e1ce22588679d9384d336df6064210fda14e640ed422e2a17d5c40d901f60e161c99482d723f4d309
  languageName: node
  linkType: hard

"temp@npm:^0.8.4":
  version: 0.8.4
  resolution: "temp@npm:0.8.4"
  dependencies:
    rimraf: "npm:~2.6.2"
  checksum: 10c0/7f071c963031bfece37e13c5da11e9bb451e4ddfc4653e23e327a2f91594102dc826ef6a693648e09a6e0eb856f507967ec759ae55635e0878091eccf411db37
  languageName: node
  linkType: hard

"tempy@npm:0.3.0":
  version: 0.3.0
  resolution: "tempy@npm:0.3.0"
  dependencies:
    temp-dir: "npm:^1.0.0"
    type-fest: "npm:^0.3.1"
    unique-string: "npm:^1.0.0"
  checksum: 10c0/9432dc82569ab0f34f23aab19ab277c58c7fcf12f903483436e9e1ee72b6b5be2189da31e351eecc69a0f98f6f2003d524cdbc50e67ee7202edf3675f9b0c2c0
  languageName: node
  linkType: hard

"tempy@npm:^0.7.1":
  version: 0.7.1
  resolution: "tempy@npm:0.7.1"
  dependencies:
    del: "npm:^6.0.0"
    is-stream: "npm:^2.0.0"
    temp-dir: "npm:^2.0.0"
    type-fest: "npm:^0.16.0"
    unique-string: "npm:^2.0.0"
  checksum: 10c0/f93764c9c236ade74037b5989799930687d8618fb9ce6040d3f2a82b0ae60f655cc07bad883a0ba55dc13dc56af2f92d8e8a534a9eff78f4ac79a19d65f7dadd
  languageName: node
  linkType: hard

"terminal-link@npm:^2.1.1":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    supports-hyperlinks: "npm:^2.0.0"
  checksum: 10c0/947458a5cd5408d2ffcdb14aee50bec8fb5022ae683b896b2f08ed6db7b2e7d42780d5c8b51e930e9c322bd7c7a517f4fa7c76983d0873c83245885ac5ee13e3
  languageName: node
  linkType: hard

"terser@npm:^5.15.0":
  version: 5.43.1
  resolution: "terser@npm:5.43.1"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/9cd3fa09ea6bcb79eb71995216b8bef0651b18c5c3877535fc699a77e1ab43b140a4da5811ac9baeb654fa9ec939b17324092f0f0bdb19c402e101e3db946986
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10c0/02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 10c0/9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: 10c0/f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"throat@npm:^5.0.0":
  version: 5.0.0
  resolution: "throat@npm:5.0.0"
  checksum: 10c0/1b9c661dabf93ff9026fecd781ccfd9b507c41b9d5e581614884fffd09f3f9ebfe26d3be668ccf904fd324dd3f6efe1a3ec7f83e91b1dff9fdcc6b7d39b8bfe3
  languageName: node
  linkType: hard

"through2@npm:^2.0.1":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: "npm:~2.3.6"
    xtend: "npm:~4.0.1"
  checksum: 10c0/cbfe5b57943fa12b4f8c043658c2a00476216d79c014895cef1ac7a1d9a8b31f6b438d0e53eecbb81054b93128324a82ecd59ec1a4f91f01f7ac113dcb14eade
  languageName: node
  linkType: hard

"tinycolor2@npm:^1.4.2":
  version: 1.6.0
  resolution: "tinycolor2@npm:1.6.0"
  checksum: 10c0/9aa79a36ba2c2a87cb221453465cabacd04b9e35f9694373e846fdc78b1c768110f81e581ea41440106c0f24d9a023891d0887e8075885e790ac40eb0e74a5c1
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.15
  resolution: "tinyglobby@npm:0.2.15"
  dependencies:
    fdir: "npm:^6.5.0"
    picomatch: "npm:^4.0.3"
  checksum: 10c0/869c31490d0d88eedb8305d178d4c75e7463e820df5a9b9d388291daf93e8b1eb5de1dad1c1e139767e4269fe75f3b10d5009b2cc14db96ff98986920a186844
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10c0/f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"traverse@npm:~0.6.6":
  version: 0.6.11
  resolution: "traverse@npm:0.6.11"
  dependencies:
    gopd: "npm:^1.2.0"
    typedarray.prototype.slice: "npm:^1.0.5"
    which-typed-array: "npm:^1.1.18"
  checksum: 10c0/2b57662da3061ed2aa9977a6a3e315fc19f2cfdeb691700a88c12f4d460146abdb4d726740f47a9ca5fa84d3c50096b76ee50047d1a71c2afb168852ad264e36
  languageName: node
  linkType: hard

"trim-right@npm:^1.0.1":
  version: 1.0.1
  resolution: "trim-right@npm:1.0.1"
  checksum: 10c0/71989ec179c6b42a56e03db68e60190baabf39d32d4e1252fa1501c4e478398ae29d7191beffe015b9d9dc76f04f4b3a946bdb9949ad6b0c0b0c5db65f3eb672
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 10c0/232509f1b84192d07b81d1e9b9677088e590ac1303436da1e92b296e9be8e31ea042e3e1fd3d29b1742ad2c959e95afe30f63117b8f1bc3a3850070a5142fea7
  languageName: node
  linkType: hard

"ts-object-utils@npm:0.0.5":
  version: 0.0.5
  resolution: "ts-object-utils@npm:0.0.5"
  checksum: 10c0/0279f8a7504b3905f2b14769769985f214154f1aedc60077c3baaced078369ae465aecc6acc04c614f40893e559d05697f6f4ef9fc411e3b6d1d15e6269a5e14
  languageName: node
  linkType: hard

"tslib@npm:2.4.0":
  version: 2.4.0
  resolution: "tslib@npm:2.4.0"
  checksum: 10c0/eb19bda3ae545b03caea6a244b34593468e23d53b26bf8649fbc20fce43e9b21a71127fd6d2b9662c0fe48ee6ff668ead48fd00d3b88b2b716b1c12edae25b5d
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.5.0":
  version: 2.6.1
  resolution: "tslib@npm:2.6.1"
  checksum: 10c0/a0382d386f5f1d6e3a39ab22bc56d1e08493da99ab3daf550e63bae6c08fdd6dd4fd20623ef387cad8262ce3fede98439257054fc025f2103cd4603b4509a052
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.16.0":
  version: 0.16.0
  resolution: "type-fest@npm:0.16.0"
  checksum: 10c0/6b4d846534e7bcb49a6160b068ffaed2b62570d989d909ac3f29df5ef1e993859f890a4242eebe023c9e923f96adbcb3b3e88a198c35a1ee9a731e147a6839c3
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^0.3.1":
  version: 0.3.1
  resolution: "type-fest@npm:0.3.1"
  checksum: 10c0/ef632e9549f331024594bbb8b620fe570d90abd8e7f2892d4aff733fd72698774e1a88e277fac02b4267de17d79cbb87860332f64f387145532b13ace6510502
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 10c0/ce6b5ef806a76bf08d0daa78d65e61f24d9a0380bd1f1df36ffb61f84d14a0985c3a921923cf4b97831278cb6fa9bf1b89c751df09407e0510b14e8c081e4e0f
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typedarray.prototype.slice@npm:^1.0.5":
  version: 1.0.5
  resolution: "typedarray.prototype.slice@npm:1.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    math-intrinsics: "npm:^1.1.0"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
  checksum: 10c0/4995828640f8079cfbc9e3b4b8fc2e0eeb109edd1a2596806325ae07306dba1cd947e6ed6f63391aa7d5af0ea4f40fddf1b6eb863f8a59869a9dfc5dcfd8eac2
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.35
  resolution: "ua-parser-js@npm:1.0.35"
  checksum: 10c0/4641332fdf163ecdec4810cc2335932754f1b71527097f06005a658de256e22f5836a4a7860619c9e611d578e0451ff39dbff1a9b83c6615e3b0b3dd29588c30
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 10c0/6363e40b2fa758eb5ec5e21b3c7fb83e5da8dcfbd866cc0c199d5534c42f03b9ea9ab069769cc388e1d7ab93b4eeef28ef506ab5f18d910ef29617715101884f
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/cb811d9d54eb5821b81b18205750be84cb015c20a4a44280794e915f5a0a70223ce39066781a354e872df3572e8155c228f43ff0cce94c7cbf4da2cc7cbdd635
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unique-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "unique-string@npm:1.0.0"
  dependencies:
    crypto-random-string: "npm:^1.0.0"
  checksum: 10c0/79cc2a6515a51e6350c74f65c92246511966c47528f1119318cbe8d68a508842f4e5a2a81857a65f3919629397a525f820505116dd89cac425294598e35ca12c
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: "npm:^2.0.0"
  checksum: 10c0/11820db0a4ba069d174bedfa96c588fc2c96b083066fafa186851e563951d0de78181ac79c744c1ed28b51f9d82ac5b8196ff3e4560d0178046ef455d8c2244b
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10c0/e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^1.0.0":
  version: 1.0.0
  resolution: "universalify@npm:1.0.0"
  checksum: 10c0/735dd9c118f96a13c7810212ef8b45e239e2fe6bf65aceefbc2826334fcfe8c523dbbf1458cef011563c51505e3a367dff7654cfb0cec5b6aa710ef120843396
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"url-join@npm:4.0.0":
  version: 4.0.0
  resolution: "url-join@npm:4.0.0"
  checksum: 10c0/1aa466cfa128adab76dc9e559b38e2171df51e6105b5773382c3726e5a29971da013e4f9f5c36f1414ef1e5f1af535cfaf29611b53b0d2fc4f311f7b41199d13
  languageName: node
  linkType: hard

"use-latest-callback@npm:^0.1.7":
  version: 0.1.7
  resolution: "use-latest-callback@npm:0.1.7"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/5b3bc6a4d5d251c9e47ff728e62884ab87279e72cbc2147c5a5c5ff02a6e322eca84b0744afb6a22ba12cc329f6ac237dbe90af60d3e64d99c3db10782be8b6b
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.2.0, use-sync-external-store@npm:^1.2.2":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/2eee5723b0fcce8256f5bfd3112af6c453b5471db00af9c3533e3d5a6e57de83513f9a145a570890457bd7abf2c2aa05797291d950ac666e5a074895dc63168b
  languageName: node
  linkType: hard

"uuid@npm:^8.0.0, uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"valid-url@npm:~1.0.9":
  version: 1.0.9
  resolution: "valid-url@npm:1.0.9"
  checksum: 10c0/3995e65f9942dbcb1621754c0f9790335cec61e9e9310c0a809e9ae0e2ae91bb7fc6a471fba788e979db0418d9806639f681ecebacc869bc8c3de88efa562ee6
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "validate-npm-package-name@npm:3.0.0"
  dependencies:
    builtins: "npm:^1.0.3"
  checksum: 10c0/064f21f59aefae6cc286dd4a50b15d14adb0227e0facab4316197dfb8d06801669e997af5081966c15f7828a5e6ff1957bd20886aeb6b9d0fa430e4cb5db9c4a
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^5.0.0":
  version: 5.0.1
  resolution: "validate-npm-package-name@npm:5.0.1"
  checksum: 10c0/903e738f7387404bb72f7ac34e45d7010c877abd2803dc2d614612527927a40a6d024420033132e667b1bade94544b8a1f65c9431a4eb30d0ce0d80093cd1f74
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 10c0/a8ec5c95d747c840198f20b4973327fa317b98397f341e7a2f352bfcf385aeb73c0eea01cc6d406c20169298375397e259efc317aec53c8ffc001ec998204aed
  languageName: node
  linkType: hard

"walker@npm:^1.0.7":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10c0/a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"warn-once@npm:^0.1.0":
  version: 0.1.1
  resolution: "warn-once@npm:0.1.1"
  checksum: 10c0/f531e7b2382124f51e6d8f97b8c865246db8ab6ff4e53257a2d274e0f02b97d7201eb35db481843dc155815e154ad7afb53b01c4d4db15fb5aa073562496aff7
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^5.0.0":
  version: 5.0.0
  resolution: "webidl-conversions@npm:5.0.0"
  checksum: 10c0/bf31df332ed11e1114bfcae7712d9ab2c37e7faa60ba32d8fdbee785937c0b012eee235c19d2b5d84f5072db84a160e8d08dd382da7f850feec26a4f46add8ff
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.17
  resolution: "whatwg-fetch@npm:3.6.17"
  checksum: 10c0/eec71f436f02cae5577b4af220e19727eb1d1c63726dc0968e97201f53d81cb53614052765d4be4d2da4f291d18cd213c6db2c9dd12a7ada5cc553f838f09524
  languageName: node
  linkType: hard

"whatwg-url-without-unicode@npm:8.0.0-3":
  version: 8.0.0-3
  resolution: "whatwg-url-without-unicode@npm:8.0.0-3"
  dependencies:
    buffer: "npm:^5.4.3"
    punycode: "npm:^2.1.1"
    webidl-conversions: "npm:^5.0.0"
  checksum: 10c0/c27a637ab7d01981b2e2f576fde2113b9c42247500e093d2f5ba94b515d5c86dbcf70e5cad4b21b8813185f21fa1b4846f53c79fa87995293457e28c889cc0fd
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10c0/087038e7992649eaffa6c7a4f3158d5b53b14cf5b6c1f0e043dccfacb1ba179d12f17545d5b85ebd94a42ce280a6fe65d0cbcab70f4fc6daad1dfae85e0e6a3e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10c0/e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wonka@npm:^4.0.14":
  version: 4.0.15
  resolution: "wonka@npm:4.0.15"
  checksum: 10c0/b93f15339c0de08259439d3c5bd3a03ca44196fbd7553cbe13c844e7b3ff2eb31b5dc4a0b2e0c3c2119160e65fc471d8366f4559744b53ab52763eb463b6793b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^2.3.0":
  version: 2.4.3
  resolution: "write-file-atomic@npm:2.4.3"
  dependencies:
    graceful-fs: "npm:^4.1.11"
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8cb4bba0c1ab814a9b127844da0db4fb8c5e06ddbe6317b8b319377c73b283673036c8b9360120062898508b9428d81611cf7fa97584504a00bc179b2a580b92
  languageName: node
  linkType: hard

"ws@npm:^6.2.2":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
  checksum: 10c0/56a35b9799993cea7ce2260197e7879f21bbbb194a967f31acbbda6f7f46ecda4365951966fb062044c95197e19fb2f053be6f65c172435455186835f494de41
  languageName: node
  linkType: hard

"ws@npm:^7, ws@npm:^7.5.10":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/bd7d5f4aaf04fae7960c23dcb6c6375d525e00f795dd20b9385902bd008c40a94d3db3ce97d878acc7573df852056ca546328b27b39f47609f80fb22a0a9b61d
  languageName: node
  linkType: hard

"ws@npm:^8.12.1":
  version: 8.18.3
  resolution: "ws@npm:8.18.3"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/eac918213de265ef7cb3d4ca348b891a51a520d839aa51cdb8ca93d4fa7ff9f6ccb339ccee89e4075324097f0a55157c89fa3f7147bde9d8d7e90335dc087b53
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: "npm:^1.1.0"
    uuid: "npm:^7.0.3"
  checksum: 10c0/51bf35cee52909aeb18f868ecf9828f93b8042fadf968159320f9f11e757a52e43f6563a53b586986cfe5a34d576f3300c4c0cf1e14300084344ae206eaa53c3
  languageName: node
  linkType: hard

"xml2js@npm:0.6.0":
  version: 0.6.0
  resolution: "xml2js@npm:0.6.0"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/db1ad659210eda4b77929aa692271308ec7e04830112161b8c707f3bcc7138947409c8461ae5c8bcb36b378d62594a8d1cb78770ff5c3dc46a68c67a0838b486
  languageName: node
  linkType: hard

"xmlbuilder@npm:^14.0.0":
  version: 14.0.0
  resolution: "xmlbuilder@npm:14.0.0"
  checksum: 10c0/3a99d1642b0a25a24f24bc5a32f37d299886e01e004654e34d13877e7648956f000708568456fedb7423e1dc2fbfe6520298699a3fbabc681d989be4a41c1509
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 10c0/665266a8916498ff8d82b3d46d3993913477a254b98149ff7cff060d9b7cc0db7cf5a3dae99aed92355254a808c0e2e3ec74ad1b04aa1061bdb8dfbea26c18b8
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10c0/308a2efd7cc296ab2c0f3b9284fd4827be01cfeb647b3ba18230e3a416eb1bc887ac050de9f8c4fd9e7856b2e8246e05d190b53c96c5ad8d8cb56dffb6f81024
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^2.2.1":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 10c0/f6f7310cf7264a8107e72c1376f4de37389945d2fb4656f8060eca83f01d2d703f9d1b925dd8f39852a57034fafefde6225409ddd9f22aebfda16c6141b71858
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10c0/25df918833592a83f52e7e4f91ba7d7bfaa2b891ebf7fe901923c2ee797534f23a176913ff6ff7ebbc1cc1725a044cc6a6539fed8bfd4e13b5b16376875f9499
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^15.1.0":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10c0/f1ca680c974333a5822732825cca7e95306c5a1e7750eb7b973ce6dc4f97a6b0a8837203c8b194f461969bfe1fb1176d1d423036635285f6010b392fa498ab2d
  languageName: node
  linkType: hard

"yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zod-validation-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "zod-validation-error@npm:2.1.0"
  peerDependencies:
    zod: ^3.18.0
  checksum: 10c0/e8e8a0af64092dfb3388d759bf10fb7cf5358bc1bdb365771b8ac1944b1fb014ccbc8e60fbd69627961ea5873c5694e5c3fe730341c9842312fbb91661a1f451
  languageName: node
  linkType: hard

"zod@npm:^3.22.4":
  version: 3.25.76
  resolution: "zod@npm:3.25.76"
  checksum: 10c0/5718ec35e3c40b600316c5b4c5e4976f7fee68151bc8f8d90ec18a469be9571f072e1bbaace10f1e85cf8892ea12d90821b200e980ab46916a6166a4260a983c
  languageName: node
  linkType: hard

"zustand@npm:^4.5.7":
  version: 4.5.7
  resolution: "zustand@npm:4.5.7"
  dependencies:
    use-sync-external-store: "npm:^1.2.2"
  peerDependencies:
    "@types/react": ">=16.8"
    immer: ">=9.0.6"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
  checksum: 10c0/55559e37a82f0c06cadc61cb08f08314c0fe05d6a93815e41e3376130c13db22a5017cbb0cd1f018c82f2dad0051afe3592561d40f980bd4082e32005e8a950c
  languageName: node
  linkType: hard
