# Android APK Build Instructions

This guide explains how to build Android APKs locally for the Netone Self-Care app.

## Prerequisites

- Android Studio or Android SDK installed
- Java Development Kit (JDK) 8 or higher
- Node.js and npm/yarn
- React Native development environment set up

## Keystore Setup

The project is configured with both debug and release keystores:

### Debug Keystore

- **File**: `android/app/debug.keystore`
- **Password**: `android`
- **Alias**: `androiddebugkey`
- **Key Password**: `android`

### Release Keystore

- **File**: `android/app/netone-release-key.keystore`
- **Password**: `netone123`
- **Alias**: `netone-key-alias`
- **Key Password**: `netone123`
- **Configuration**: `android/keystore.properties`

## Building APKs

### Available Build Commands

```bash
# Build debug APK
npm run build:android:debug

# Build release APK (production-ready)
npm run build:android:release

# Clean build cache
npm run build:android:clean
```

### Build Outputs

After building, APK files will be located at:

- **Debug APK**: `android/app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `android/app/build/outputs/apk/release/app-release.apk`

## Build Process

1. **Clean Build** (recommended before building):

   ```bash
   npm run build:android:clean
   ```

2. **Build Debug APK**:

   ```bash
   npm run build:android:debug
   ```

3. **Build Release APK**:
   ```bash
   npm run build:android:release
   ```

## Key Differences Between Debug and Release

### Debug APK

- Uses debug keystore (default Android debug key)
- Includes debugging information
- Larger file size
- Suitable for development and testing

### Release APK

- Uses production keystore with custom certificate
- Optimized and minified
- Smaller file size
- Ready for distribution
- Signed with production certificate

## Security Notes

- The `keystore.properties` file and `*.keystore` files are added to `.gitignore` for security
- Keep production keystore passwords secure
- Backup the production keystore file safely
- Never commit keystores or passwords to version control

## Troubleshooting

### Common Issues

1. **Keystore password errors**:

   - Ensure passwords in `keystore.properties` match the actual keystore passwords
   - Verify keystore file exists in the correct location

2. **Build failures**:

   - Run `npm run build:android:clean` first
   - Check that all dependencies are installed
   - Ensure Android SDK is properly configured

3. **Memory issues**:
   - Increase JVM heap size in `android/gradle.properties`:
     ```
     org.gradle.jvmargs=-Xmx4g -XX:MaxPermSize=512m
     ```

## Installation

### Installing APKs on Device

1. **Enable Unknown Sources** on your Android device
2. **Transfer APK** to device via:

   - USB cable and file transfer
   - Email attachment
   - Cloud storage (Google Drive, Dropbox, etc.)
   - ADB command: `adb install path/to/app.apk`

3. **Install APK** by tapping on it in file manager

### ADB Installation Commands

```bash
# Install debug APK
adb install android/app/build/outputs/apk/debug/app-debug.apk

# Install release APK
adb install android/app/build/outputs/apk/release/app-release.apk

# Install with replacement of existing app
adb install -r path/to/app.apk
```

## Additional Resources

- [React Native Android Setup](https://reactnative.dev/docs/environment-setup)
- [Android App Signing](https://developer.android.com/studio/publish/app-signing)
- [Expo Development Build](https://docs.expo.dev/development/build/)
