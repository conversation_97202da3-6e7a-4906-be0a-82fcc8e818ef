import axios from "axios";
import LocalStore from "./store";
import { useSessionStore } from "./zustandStore";

const baseURL = "https://apps.netone.co.zw/api";
const baseURL2 = "http://10.95.1.232:8201/zec";
const baseURL3 = "https://onboarding.onemoney.co.zw/api";
export const instance3 = axios.create({
  baseURL: baseURL3,
});

const instance = axios.create({
  baseURL: baseURL,
});

instance3.interceptors.request.use(
  async function (config) {
    const token = await LocalStore.getData("@userToken");
    if (!token) {
    } else {
      if (!config.headers.Authorization) {
        config.headers.Authorization = "Bearer " + token.token;
      }
    }
    return config;
  },
  async function (error) {}
);

instance.interceptors.request.use(
  async function (config) {
    //TODO:exclude all otp routes
    if (
      config.url === "/auth/signin" ||
      config.url === "/auth/v2/signup" ||
      config.url === "/auth/verify-phone-number" ||
      config.url === "/auth/forgot-password" ||
      config.url === "/auth/change-device"
    ) {
      return config;
    }
    const token = await LocalStore.getData("@userToken");

    if (!token) {
    } else {
      if (!config.headers.Authorization) {
        config.headers.Authorization = "Bearer " + token.token;
      }
    }

    return config;
  },
  async function (error) {}
);

instance.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    if (error.response.status == 401) {
      useSessionStore.setState({ timeout: true });
    }
    return Promise.reject(error);
  }
);

export default instance;

export const instanceZec = axios.create({
  baseURL: baseURL2,
});
